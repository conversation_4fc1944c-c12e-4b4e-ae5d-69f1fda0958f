"use server";

import <PERSON><PERSON> from 'stripe';
import env from '~/env'; // Assuming you have env setup for Stripe keys

if (!env.STRIPE_SECRET_KEY) {
  throw new Error('Stripe secret key is not configured in environment variables.');
}

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2022-08-01', // Use a specific API version
});

/**
 * Lists Stripe Checkout Sessions.
 * Supports pagination via the 'starting_after' parameter and filtering by customer ID.
 * @param customerId - Optional Stripe Customer ID to filter sessions for.
 * @param startingAfter - Optional ID of the session to start the list after.
 * @param limit - Optional number of sessions to retrieve (default 1000, max 100).
 * @returns An array of checkout session data.
 */
export async function listCheckoutSessions(customerId?: string, startingAfter?: string, limit: number = 1000) {
  try {
    const params: Stripe.Checkout.SessionListParams = {
      limit: Math.min(limit, 100), // Ensure limit doesn't exceed 100
      starting_after: startingAfter,
    };
    if (customerId) {
      params.customer = customerId;
      console.log(`Fetching checkout sessions for customer ${customerId}${startingAfter ? ` starting after ${startingAfter}` : ''}, limit: ${limit}`);
    } else {
      console.log(`Fetching all checkout sessions${startingAfter ? ` starting after ${startingAfter}` : ''}, limit: ${limit}`);
    }

    const sessions = await stripe.checkout.sessions.list(params);
    console.log(`Fetched ${sessions.data.length} sessions.`);
    // Return only the data array, which is serializable
    return sessions.data;
  } catch (error: any) {
    console.error("Error listing Stripe checkout sessions:", error);
    throw new Error(`Failed to list Stripe checkout sessions: ${error.message}`);
  }
}

/**
 * Expires a specific Stripe Checkout Session.
 * @param sessionId - The ID of the Checkout Session to expire.
 * @returns The expired Checkout Session object.
 */
export async function expireCheckoutSession(sessionId: string) {
  if (!sessionId) {
    throw new Error("Session ID is required to expire a checkout session.");
  }
  try {
    console.log(`Expiring checkout session: ${sessionId}`);
    const session = await stripe.checkout.sessions.expire(sessionId);
    console.log(`Successfully expired session: ${sessionId}, Status: ${session.status}`);
    return session;
  } catch (error: any) {
    console.error(`Error expiring Stripe checkout session ${sessionId}:`, error);
    // Handle specific Stripe errors if needed, e.g., session already expired
    if (error.code === 'checkout_session_invalid_state') {
       throw new Error(`Session ${sessionId} cannot be expired (likely already expired or completed).`);
    }
    throw new Error(`Failed to expire Stripe checkout session ${sessionId}: ${error.message}`);
  }
}
export interface DuplicateSubscription {
  email: string;
  subscriptions: Stripe.Subscription[];
}

/**
 * Finds all active Stripe subscriptions for which there are more than one
 * active subscription for the same email address.
 * @returns An array of duplicate subscription data.
 */
export async function findDuplicateActiveSubscriptions(): Promise<DuplicateSubscription[]> {
  try {
    console.log("Fetching all active Stripe subscriptions...");
    const allSubscriptions: Stripe.Subscription[] = [];
    let startingAfter: string | undefined;
    let hasMore = true;

    while (hasMore) {
      const params: Stripe.SubscriptionListParams = {
        status: 'active',
        limit: 100, // Max limit
        starting_after: startingAfter,
        expand: ['data.customer'], // Expand customer to get email
      };
      const subscriptions = await stripe.subscriptions.list(params);
      allSubscriptions.push(...subscriptions.data);
      startingAfter = subscriptions.data.length > 0 ? subscriptions.data[subscriptions.data.length - 1].id : undefined;
      hasMore = subscriptions.has_more;
      console.log(`Fetched ${subscriptions.data.length} active subscriptions. Total fetched: ${allSubscriptions.length}. Has more: ${hasMore}`);
    }

    console.log(`Total active subscriptions fetched: ${allSubscriptions.length}`);

    // Group subscriptions by customer email
    const emailGroups: { [key: string]: Stripe.Subscription[] } = {};
    for (const sub of allSubscriptions) {
      // Ensure customer is expanded and is a Customer object
      if (sub.customer && typeof sub.customer === 'object' && 'email' in sub.customer && sub.customer.email) {
        const email = sub.customer.email.toLowerCase();
        if (!emailGroups[email]) {
          emailGroups[email] = [];
        }
        emailGroups[email].push(sub);
      } else {
        console.warn(`Subscription ${sub.id} is missing customer email or customer object is not expanded/valid.`);
      }
    }

    // Filter for emails with multiple active subscriptions
    const duplicates: DuplicateSubscription[] = Object.entries(emailGroups)
      .filter(([_, subs]) => subs.length > 1)
      .map(([email, subs]) => ({
        email,
        subscriptions: subs.sort((a, b) => (a.created || 0) - (b.created || 0)), // Sort by creation date
      }));

    console.log(`Found ${duplicates.length} emails with duplicate active subscriptions.`);
    return duplicates;

  } catch (error: any) {
    console.error("Error finding duplicate active Stripe subscriptions:", error);
    throw new Error(`Failed to find duplicate active Stripe subscriptions: ${error.message}`);
  }
}