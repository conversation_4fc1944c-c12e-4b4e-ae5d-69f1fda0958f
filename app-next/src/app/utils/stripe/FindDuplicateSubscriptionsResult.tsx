"use client";

import type { Strip<PERSON> } from 'stripe';

interface DuplicateSubscription {
  email: string;
  subscriptions: Stripe.Subscription[];
}

interface Props {
  result: DuplicateSubscription[] | null;
  error?: string | null;
}

export default function FindDuplicateSubscriptionsResult({ result, error }: Props) {
  if (error) {
    return (
      <div className="mt-4 p-4 border rounded-md bg-red-50 text-red-700">
        <h3 className="text-md font-semibold mb-2">Error Finding Duplicate Subscriptions:</h3>
        <p>{error}</p>
      </div>
    );
  }

  if (!result) {
    return null;
  }

  if (result.length === 0) {
    return (
      <div className="mt-4 p-4 border rounded-md bg-green-50 text-green-700">
        <h3 className="text-md font-semibold mb-2">Result:</h3>
        <p>No duplicate active subscriptions found.</p>
      </div>
    );
  }

  return (
    <div className="mt-4 p-4 border rounded-md bg-gray-50">
      <h3 className="text-md font-semibold mb-2">Duplicate Active Subscriptions Found: {result.length} email(s)</h3>
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {result.map((dup) => (
          <div key={dup.email} className="p-3 border rounded-md bg-white">
            <h4 className="text-sm font-semibold text-red-600">Email: {dup.email} ({dup.subscriptions.length} active subscriptions)</h4>
            <ul className="list-disc list-inside text-xs mt-1 space-y-1">
              {dup.subscriptions.map((sub) => (
                <li key={sub.id}>
                  ID: {sub.id} | Status: {sub.status} | Created: {new Date(sub.created * 1000).toLocaleDateString()}
                  {sub.current_period_end && <span> | Period End: {new Date(sub.current_period_end * 1000).toLocaleDateString()}</span>}
                  {sub.items.data.length > 0 && (
                    <ul className="list-circle list-inside ml-4">
                      {sub.items.data.map(item => (
                        <li key={item.id}>
                          Item: {item.price?.product && typeof item.price.product === 'object' && 'name' in item.price.product ? item.price.product.name : item.price?.id} (ID: {item.id})
                           | Qty: {item.quantity}
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
}