"use client";
import { useState } from 'react';
import { addSkuMaps, addUPC, updateShopBarcode } from './addSkuMapsUtil'; // Assuming these are still needed
import LoadingButton from '~/components/loadingButton';
import { addShopifyPaymentTransaction } from './poUtils'; // Assuming this is still needed
import { toast } from 'sonner';
import { pushShopifyTransactions } from './poUtils'; // Assuming this is still needed
import ShopifyInitialSyncForm from './ShopifyInitialSyncForm';
import FindOldInvoicesResult from './FindOldInvoicesResult';
import ProcessOldInvoicesResult from './ProcessOldInvoicesResult'; // Import the new component
import DeleteXeroInvoiceForm from './xero/DeleteXeroInvoiceForm'; // Import the new Xero delete form
import { getTransactionsForOrder, voidTransaction, refundTransaction, refund, updateShopifyOrderProcessedAt } from '~/data/shopify/shopify'; // Removed REST update action import
import { updateShopifyOrderProcessedAtGraphQL, getOrderById } from '~/data/shopify/shopifyGrapql'; // Added GraphQL update action import
import { findDuplicateActiveSubscriptions, type DuplicateSubscription } from './stripe/actions';
import FindDuplicateSubscriptionsResult from './stripe/FindDuplicateSubscriptionsResult';

// Define type for find results state
interface InvoiceInfo {
  invoiceNumber: string | undefined;
  invoiceID: string | undefined;
  status: string | undefined;
  total: number | undefined;
}
interface FindResult {
  count: number;
  invoices: InvoiceInfo[];
  errorCount: number;
  fetchError?: string;
  error?: string;
  details?: string;
}

// Define type for process results state
interface ProcessResult {
  deletedCount: number;
  voidedCount: number;
  errorCount: number;
  errors: Array<{ invoiceNumber: string | undefined, error: string }>;
  apiError?: string; // For top-level API errors
}

// Define a basic type for server action results
interface ServerActionResult {
  success: boolean;
  message?: string;
  error?: string;
}

export default function Home() {
  // State for Add Payment Transaction section
  const [orderId, setOrderId] = useState('');
  const [transactionDate, setTransactionDate] = useState('');
  const [isLoadingAddPayment, setIsLoadingAddPayment] = useState(false);

  // State for Find/Process Old Invoices section
  const [isLoadingFind, setIsLoadingFind] = useState(false);
  const [findResult, setFindResult] = useState<FindResult | null>(null);
  const [isLoadingProcess, setIsLoadingProcess] = useState(false);
  const [processResult, setProcessResult] = useState<ProcessResult | null>(null);
  const [searchType, setSearchType] = useState<'prefix' | 'contains'>('prefix');
  const [invoicePrefix, setInvoicePrefix] = useState('shop-');
  const [invoiceContainsText, setInvoiceContainsText] = useState('');
  const [startDate, setStartDate] = useState(() => new Date().toISOString().split('T')[0]); // Default to today


  // State for Push Shopify Transactions button (if needed)
  const [isLoadingPush, setIsLoadingPush] = useState(false);

  // State for Show/Void Transactions section
  const [showTxOrderId, setShowTxOrderId] = useState('');
  const [transactionsResult, setTransactionsResult] = useState<any[] | null>(null); // Adjust 'any' type later if possible
  const [isLoadingShowTx, setIsLoadingShowTx] = useState(false);
  const [voidOrderId, setVoidOrderId] = useState('');
  const [orderResult, setOrderResult] = useState<any | null>(null); // State for Get Order result
  const [isLoadingGetOrder, setIsLoadingGetOrder] = useState(false); // Loading state for Get Order
  const [voidTxId, setVoidTxId] = useState('');
  const [isLoadingVoidTx, setIsLoadingVoidTx] = useState(false);

  // State for Update Order Processed At section
  const [updateOrderId, setUpdateOrderId] = useState('');
  // No Transaction ID state needed
  const [newProcessedAtDate, setNewProcessedAtDate] = useState('');
  const [isLoadingUpdateProcessedAt, setIsLoadingUpdateProcessedAt] = useState(false);

  // State for Find Duplicate Stripe Subscriptions
  const [isLoadingFindStripeDuplicates, setIsLoadingFindStripeDuplicates] = useState(false);
  const [stripeDuplicatesResult, setStripeDuplicatesResult] = useState<DuplicateSubscription[] | null>(null);
  const [stripeDuplicatesError, setStripeDuplicatesError] = useState<string | null>(null);


  // --- Handlers ---

  function addSkuMapsClick() {
    // addSkuMaps(); // Keep or remove if not needed
    toast.info("Add Sku Maps function called (placeholder).");
  }
  function addUPCClick() {
    addUPC();
  }
  function updateShopBarcodeClick() {
    updateShopBarcode();
  }

  function handlePushShopifyTransactions() {
    setIsLoadingPush(true);
    toast.promise(pushShopifyTransactions(), {
        loading: 'Pushing Shopify Transactions...',
        success: 'Successfully pushed transactions.',
        error: (err) => `Error pushing transactions: ${err.message}`,
        finally: () => setIsLoadingPush(false)
    });
  }

  function handleAddPaymentTransaction() {
    setIsLoadingAddPayment(true);
    const orderIdNum = parseInt(orderId);
    if (isNaN(orderIdNum)) {
      toast.error('Invalid Order ID');
      setIsLoadingAddPayment(false);
      return;
    }
    if (!transactionDate) {
      toast.error('Invalid Transaction Date');
      setIsLoadingAddPayment(false);
      return;
    }
    const date = new Date(transactionDate);
    toast.promise(addShopifyPaymentTransaction(orderIdNum, date), {
      loading: 'Adding payment transaction...',
      success: (res) => {
        if (res.success) {
          return `Payment transaction added for order ${orderIdNum}`;
        } else {
          throw new Error(res.error || 'Unknown error adding payment');
        }
      },
      error: (err) => `Error adding payment transaction: ${err.message}`,
      finally: () => setIsLoadingAddPayment(false)
    });
  }

  async function handleFindOldInvoices() {
    setIsLoadingFind(true);
    setFindResult(null); // Clear previous find results
    setProcessResult(null); // Clear previous process results

    // Validate start date
    if (!startDate) {
        toast.error("Start Date is required.");
        setIsLoadingFind(false);
        return;
    }

    let criteriaInfo = '';
    let requestBody: { searchType: 'prefix' | 'contains'; value: string; startDate: string };

    if (searchType === 'prefix') {
        criteriaInfo = `prefix '${invoicePrefix}'`;
        if (!invoicePrefix) {
            toast.error("Prefix cannot be empty when searching by prefix.");
            setIsLoadingFind(false);
            return;
        }
        requestBody = { searchType: 'prefix', value: invoicePrefix, startDate };
    } else { // searchType === 'contains'
        criteriaInfo = `reference containing '${invoiceContainsText}'`;
         if (!invoiceContainsText) {
            toast.error("Search text cannot be empty when searching by contains.");
            setIsLoadingFind(false);
            return;
        }
        requestBody = { searchType: 'contains', value: invoiceContainsText, startDate };
    }

    toast.info(`Finding invoices (DRAFT, SUBMITTED, AUTHORISED, PAID) from ${startDate} with ${criteriaInfo}...`); // Use criteriaInfo here

    try {
      const response = await fetch('/api/xero/find-old-shop-invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      });
      const result: FindResult = await response.json();

      if (!response.ok) {
        throw new Error(result.details || result.error || `API Error: ${response.statusText}`);
      }

      setFindResult(result);
      toast.success(`Found ${result.count} invoices matching criteria (from ${startDate}). Fetch Errors: ${result.errorCount}`); // Updated toast

    } catch (error: any) {
      console.error("Find failed:", error);
      toast.error(`Find failed: ${error.message}`);
      setFindResult({ count: 0, invoices: [], errorCount: 1, error: error.message });
    } finally {
      setIsLoadingFind(false);
    }
  }

  async function handleProcessFoundInvoices() {
      if (!findResult || findResult.count === 0 || findResult.invoices.length === 0) {
          toast.warning("No invoices found to process. Please run 'Find Invoices' first.");
          return;
      }

      const invoicesToProcess = findResult.invoices.filter(inv => inv.invoiceID); // Ensure we only send invoices with IDs

      if (invoicesToProcess.length === 0) {
          toast.warning("Found invoices list is empty or lacks IDs.");
          return;
      }

      const draftCount = invoicesToProcess.filter(inv => inv.status === 'DRAFT').length;
      const authCount = invoicesToProcess.filter(inv => inv.status === 'AUTHORISED').length;
      const paidCount = invoicesToProcess.filter(inv => inv.status === 'PAID').length; // Count PAID invoices
      const voidTotal = authCount + paidCount; // Total to be voided

      // --- SAFETY CONFIRMATION ---
      // Updated confirmation message
      if (!window.confirm(`Are you SURE you want to process ${invoicesToProcess.length} found invoices?\n- ${draftCount} DRAFT invoices will be DELETED.\n- ${voidTotal} AUTHORISED/PAID invoices will be VOIDED.\nThis action cannot be easily undone.`)) {
          toast.warning("Processing cancelled.");
          return;
      }
      // --- END SAFETY CONFIRMATION ---

      setIsLoadingProcess(true);
      setProcessResult(null); // Clear previous process results
      // Updated info toast
      toast.info(`Processing ${invoicesToProcess.length} invoices (Deleting ${draftCount}, Voiding ${voidTotal})...`);

      try {
          const response = await fetch('/api/xero/process-found-shop-invoices', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ invoicesToProcess: invoicesToProcess }), // Send the list
          });
          const result: ProcessResult = await response.json();

          if (!response.ok) {
              throw new Error(result.apiError || `API Error: ${response.statusText}`); // Use apiError if present
          }

          setProcessResult(result); // Store results in state
          // Update success/error toasts
          // Updated success/error toasts
          if (result.errorCount > 0) {
              toast.error(`Processing completed with ${result.errorCount} errors. Deleted (DRAFT): ${result.deletedCount}, Voided (AUTH/PAID): ${result.voidedCount}.`);
          } else {
              toast.success(`Processing completed successfully. Deleted (DRAFT): ${result.deletedCount}, Voided (AUTH/PAID): ${result.voidedCount}.`);
          }

      } catch (error: any) {
          console.error("Processing failed:", error);
          toast.error(`Processing failed: ${error.message}`);
          setProcessResult(prev => ({
              deletedCount: prev?.deletedCount ?? 0,
              voidedCount: prev?.voidedCount ?? 0,
              errors: prev?.errors ?? [],
              errorCount: (prev?.errorCount ?? 0) + 1,
              apiError: error.message
          }));
      } finally {
          setIsLoadingProcess(false);
      }
  }

  async function handleShowTransactions() {
    setIsLoadingShowTx(true);
    setTransactionsResult(null); // Clear previous results
    const orderIdNum = parseInt(showTxOrderId);
    if (isNaN(orderIdNum)) {
      toast.error('Invalid Order ID');
      setIsLoadingShowTx(false);
      return;
    }

    toast.promise(getTransactionsForOrder(orderIdNum), {
      loading: `Fetching transactions for order ${orderIdNum}...`,
      success: (res: any[]) => {
        console.log("Transactions:res", res)
        const processedTransactions = [];
        const childTransactions = new Map<number, any[]>(); // Map parent_id -> [child_tx, ...]

        // First pass: group children by parent_id
        for (const tx of res) {
          if (tx.parent_id) {
            if (!childTransactions.has(tx.parent_id)) {
              childTransactions.set(tx.parent_id, []);
            }
            // Ensure the map value is treated as an array
            const childrenArray = childTransactions.get(tx.parent_id);
            if (childrenArray) {
                childrenArray.push(tx);
            }
          }
        }

        // Second pass: process potential parents and attach child info
        for (const tx of res) {
          // Explicitly skip child transactions that should be aggregated, not shown as separate rows
          if (tx.parent_id && (tx.kind === 'refund' || tx.kind === 'void')) {
              continue; // Don't create a row for these children
          }

          // If we reach here, tx is a potential parent row (sale, capture, authorization, external, etc.)
          const children = childTransactions.get(tx.id) || [];
          const refundChild = children.find(child => child.kind === 'refund' && child.status === 'success');
          // Assuming 'void' is a kind used for voiding transactions. Adjust if Shopify uses status or another field.
          // Let's also check status for void, similar to refund.
          const voidChild = children.find(child => child.kind === 'void' && child.status === 'success');

          processedTransactions.push({
              id: tx.id,
              kind: tx.kind,
              amount: tx.amount,
              currency: tx.currency, // Add currency if available
              status: tx.status,
              gateway: tx.gateway,
              createdAt: tx.created_at ? new Date(tx.created_at).toLocaleDateString() : 'N/A',
              processedAt: tx.processed_at ? new Date(tx.processed_at).toLocaleDateString() : 'N/A',
              refundedAmount: refundChild ? refundChild.amount : null,
              refundDate: refundChild?.processed_at ? new Date(refundChild.processed_at).toLocaleDateString() : null,
              isVoided: !!voidChild,
              voidDate: voidChild?.processed_at ? new Date(voidChild.processed_at).toLocaleDateString() : null,
              // Add original created_at for sorting if needed
              _originalCreatedAt: tx.created_at ? new Date(tx.created_at) : new Date(0),
          });
        }

        // Sort by creation date
        processedTransactions.sort((a, b) => a._originalCreatedAt.getTime() - b._originalCreatedAt.getTime());

        setTransactionsResult(processedTransactions);
        return `Processed ${processedTransactions.length} primary transactions for order ${orderIdNum}.`;
      },
      error: (err) => {
        setTransactionsResult([]); // Indicate error state with empty array
        return `Error fetching transactions: ${err.message}`;
      },
      finally: () => setIsLoadingShowTx(false)
    });
  }

  async function handleGetOrder() {
    setIsLoadingGetOrder(true);
    setOrderResult(null); // Clear previous results
    const orderIdStr = showTxOrderId.trim(); // Use the same Order ID input

    if (!orderIdStr) {
        toast.error('Order ID is required.');
        setIsLoadingGetOrder(false);
        return;
    }

    // Construct the Order GID (assuming getOrderById expects the GID format)
    const orderGid = `gid://shopify/Order/${orderIdStr}`;

    toast.promise(getOrderById(orderGid), {
      loading: `Fetching order ${orderIdStr}...`,
      success: (res: any) => {
        setOrderResult(res);
        return `Successfully fetched order ${orderIdStr}.`;
      },
      error: (err) => `Error fetching order: ${err.message}`,
      finally: () => setIsLoadingGetOrder(false)
    });
  }

  async function handleVoidTransaction(voidType: 'void' | 'refund') {
    const orderIdStr = voidOrderId.trim();
    const txIdStr = voidTxId.trim();

    if (!orderIdStr || !txIdStr) {
        toast.error('Order ID and Transaction ID are required.');
        return;
    }

    // Validate numeric IDs
    const orderIdNum = parseInt(orderIdStr);
    const txIdNum = parseInt(txIdStr);

    if (isNaN(orderIdNum)) {
      toast.error('Invalid Order ID format. Must be a number.');
      return;
    }
    if (isNaN(txIdNum)) {
      toast.error('Invalid Transaction ID format. Must be a number.');
      return;
    }

    // --- SAFETY CONFIRMATION ---
    // const actionText = voidType === 'void' ? 'VOID' : 'REFUND';
    // if (!window.confirm(`Are you SURE you want to attempt to ${actionText} transaction ${txIdNum} for order ${orderIdNum}?\nThis might not work as expected depending on the transaction state and Shopify API behavior.`)) {
    //     toast.warning(`${actionText} cancelled.`);
    //     return;
    // }
    // --- END SAFETY CONFIRMATION ---

    setIsLoadingVoidTx(true);
    let promiseAction: Promise<any>; // Use 'any' for simplicity here, or create a common interface

    if (voidType === 'void') {
      // Assuming voidTransaction still expects string order ID based on prior code/comments
      // If it also expects number, change orderIdStr to orderIdNum
      promiseAction = voidTransaction(orderIdStr, txIdNum);
    } else if (voidType === 'refund') {
      // refund expects number order ID based on TS error
      promiseAction = refund(orderIdNum, txIdNum);
    } else {
      toast.error('Invalid action type');
      setIsLoadingVoidTx(false);
      return;
    }

    toast.promise(promiseAction, {
      loading: `Attempting to ${voidType} transaction ${txIdNum} for order ${orderIdNum}...`,
      success: (res) => {
        // Optionally re-fetch transactions for the order to show updated status
        // handleShowTransactions(); // Need to ensure showTxOrderId is set correctly if reusing
        // Check if the response has a specific success message or structure
        if (typeof res === 'object' && res?.success) {
            return `${voidType.charAt(0).toUpperCase() + voidType.slice(1)} successful for transaction ${txIdNum}.`;
        }
        return `${voidType.charAt(0).toUpperCase() + voidType.slice(1)} request sent for transaction ${txIdNum}. Check Shopify admin for status.`;
      },
      error: (err) => `Error ${voidType}ing transaction: ${err.message}`,
      finally: () => setIsLoadingVoidTx(false)
    });
  }

  // Removed duplicate import, already added at the top

  async function handleUpdateProcessedAt() {
    setIsLoadingUpdateProcessedAt(true);
    const orderIdStr = updateOrderId.trim();
    const dateStr = newProcessedAtDate.trim();

    // Combined checks
    if (!orderIdStr || !dateStr) {
      toast.error('Order ID and New Processed At Date are required.');
      setIsLoadingUpdateProcessedAt(false);
      return;
    }

    // Validate IDs and Date
    const orderIdNum = parseInt(orderIdStr);
    const date = new Date(dateStr);

    if (isNaN(orderIdNum)) {
        toast.error('Invalid Order ID format. Must be a number.');
        setIsLoadingUpdateProcessedAt(false);
        return;
    }
    // No txIdNum validation needed
    if (isNaN(date.getTime())) {
      toast.error('Invalid Date format.');
      setIsLoadingUpdateProcessedAt(false);
      return;
    }

    // Construct the Order GID
    // const orderGid = `gid://shopify/Order/${orderIdNum}`;

    // Call the GraphQL server action
    toast.promise(
      updateShopifyOrderProcessedAt(orderIdNum, date.toISOString()),
      {
        loading: `Updating processedAt date for order ${orderIdNum}...`, // Updated loading message
        success: (res: ServerActionResult | any) => { // Use the defined type, allow 'any' for flexibility initially
            // Check for explicit success property from server action
            if (res?.success === true) {
                return res.message || `Successfully updated processedAt date for order ${orderIdNum}.`; // Updated success message
            } else {
                // Handle explicit failure or unexpected structure
                throw new Error(res?.error || 'Update failed or received unexpected response from server.');
            }
        },
        error: (err) => `Error updating date: ${err.message}`, // Catches thrown errors or promise rejection
        finally: () => setIsLoadingUpdateProcessedAt(false),
      }
    );
  }

  async function handleFindDuplicateStripeSubscriptions() {
    setIsLoadingFindStripeDuplicates(true);
    setStripeDuplicatesResult(null);
    setStripeDuplicatesError(null);
    toast.promise(findDuplicateActiveSubscriptions(), {
      loading: 'Finding duplicate active Stripe subscriptions...',
      success: (data: DuplicateSubscription[]) => {
        setStripeDuplicatesResult(data);
        if (data.length > 0) {
          return `Found ${data.length} email(s) with duplicate active subscriptions.`;
        }
        return 'No duplicate active subscriptions found.';
      },
      error: (err: any) => {
        setStripeDuplicatesError(err.message || 'Failed to find duplicate subscriptions.');
        return `Error: ${err.message || 'Failed to find duplicate subscriptions.'}`;
      },
      finally: () => {
        setIsLoadingFindStripeDuplicates(false);
      }
    });
  }

  // --- Render ---
  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="flex flex-col gap-8 w-full max-w-4xl">

        {/* --- Top Actions --- */}
        <div className="flex flex-row gap-4 flex-wrap border p-4 rounded-md">
           <h2 className="text-lg font-semibold w-full mb-2">General Utilities</h2>
          {/* <button onClick={addSkuMapsClick}>Add Sku Maps</button> */}
          <button onClick={addUPCClick} className="bg-gray-200 hover:bg-gray-300 text-black font-bold py-2 px-4 rounded">Add UPC (from file)</button>
          <button onClick={updateShopBarcodeClick} className="bg-gray-200 hover:bg-gray-300 text-black font-bold py-2 px-4 rounded">Update Shopify Barcodes (from file)</button>
          <LoadingButton onClick={handlePushShopifyTransactions} loading={isLoadingPush} className="bg-gray-200 hover:bg-gray-300 text-black font-bold py-2 px-4 rounded">
            Push Shopify Tx to Firebase
          </LoadingButton>
        </div>

        {/* --- Shopify Initial Invoice Sync --- */}
        <ShopifyInitialSyncForm />

        {/* --- Find and Process Old Invoices --- */}
        <div className="flex flex-col gap-4 border p-4 rounded-md border-orange-500">
          <h2 className="text-lg font-semibold text-orange-700">Find & Process Xero Invoices</h2>
          <p className="text-sm text-gray-600">Step 1: Find DRAFT, SUBMITTED, AUTHORISED, or PAID invoices based on criteria for review.</p>

          {/* Search Type Selection */}
          <div className="flex gap-4 items-center">
              <span className="font-medium">Search by:</span>
              <label className="flex items-center gap-1 cursor-pointer">
                  <input
                      type="radio"
                      name="searchType"
                      value="prefix"
                      checked={searchType === 'prefix'}
                      onChange={() => setSearchType('prefix')}
                      className="cursor-pointer"
                  />
                  Prefix
              </label>
              <label className="flex items-center gap-1 cursor-pointer">
                  <input
                      type="radio"
                      name="searchType"
                      value="contains"
                      checked={searchType === 'contains'}
                      onChange={() => setSearchType('contains')}
                      className="cursor-pointer"
                  />
                  Reference Contains
              </label>
          </div>

          {/* Conditional Input Fields */}
          {searchType === 'prefix' && (
              <div className="flex flex-row gap-4 items-center">
                  <label htmlFor="invoicePrefix" className="w-36">Invoice Prefix:</label>
                  <input
                      id="invoicePrefix"
                      type="text"
                      value={invoicePrefix}
                      onChange={(e) => setInvoicePrefix(e.target.value)}
                      className="border border-gray-300 px-2 py-1 rounded"
                      placeholder="e.g., shop-"
                  />
              </div>
          )}
          {searchType === 'contains' && (
              <div className="flex flex-row gap-4 items-center">
                  <label htmlFor="invoiceContainsText" className="w-36">Reference Contains:</label>
                  <input
                      id="invoiceContainsText"
                      type="text"
                      value={invoiceContainsText}
                      onChange={(e) => setInvoiceContainsText(e.target.value)}
                      className="border border-gray-300 px-2 py-1 rounded"
                      placeholder="e.g., specific customer name or PO"
                  />
              </div>
          )}

          {/* Start Date Input */}
          <div className="flex flex-row gap-4 items-center">
              <label htmlFor="startDate" className="w-36">Start Date:</label>
              <input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="border border-gray-300 px-2 py-1 rounded"
                  required // Make it visually clear it's required
              />
          </div>

          <LoadingButton onClick={handleFindOldInvoices} loading={isLoadingFind} className="bg-yellow-500 hover:bg-yellow-700 text-black font-bold py-2 px-4 rounded self-start">Find Invoices</LoadingButton>
          <FindOldInvoicesResult result={findResult} />

          <hr className="my-4"/>

          {/* Updated description for Step 2 */}
          <p className="text-sm text-gray-600">Step 2: Process the invoices found above (Delete DRAFT, Void AUTHORISED &amp; PAID).</p>
           <LoadingButton
            onClick={handleProcessFoundInvoices}
            loading={isLoadingProcess}
            disabled={!findResult || findResult.count === 0 || isLoadingFind} // Disable if nothing found or find is running
            className={`bg-red-600 hover:bg-red-800 text-white font-bold py-2 px-4 rounded self-start ${(!findResult || findResult.count === 0 || isLoadingFind) ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            Process Found Invoices (Delete/Void)
          </LoadingButton>
          <ProcessOldInvoicesResult result={processResult} />
        </div>

        {/* --- Xero Utilities --- */}
        <div className="flex flex-col gap-4 border p-4 rounded-md border-sky-500">
          <h2 className="text-lg font-semibold text-sky-700">Xero Utilities</h2>
          <DeleteXeroInvoiceForm />
        </div>

        {/* --- Stripe Utilities --- */}
        <div className="flex flex-col gap-4 border p-4 rounded-md border-indigo-500">
          <h2 className="text-lg font-semibold text-indigo-700">Stripe Utilities</h2>
          <div>
            <p className="text-sm text-gray-600 mb-2">
              Find all Stripe subscriptions where there is more than one <strong>active</strong> subscription
              for the same email address.
            </p>
            <LoadingButton
              onClick={handleFindDuplicateStripeSubscriptions}
              loading={isLoadingFindStripeDuplicates}
              className="bg-indigo-600 hover:bg-indigo-800 text-white font-bold py-2 px-4 rounded self-start"
            >
              Find Duplicate Active Subscriptions
            </LoadingButton>
          </div>
          <FindDuplicateSubscriptionsResult result={stripeDuplicatesResult} error={stripeDuplicatesError} />
        </div>

        {/* --- Add Manual Payment Transaction --- */}
        <div className="flex flex-col gap-4 border p-4 rounded-md">
          <h2 className="text-lg font-semibold">Add Shopify Payment Transaction (Manual)</h2>
          <div className="flex flex-row gap-4 items-center">
            <label htmlFor="orderId" className="w-28">Order ID (Number):</label>
            <input
              id="orderId"
              type="text"
              value={orderId}
              onChange={(e) => setOrderId(e.target.value)}
              className="border border-gray-300 px-2 py-1 rounded"
              placeholder="e.g., 12345"
            />
          </div>
          <div className="flex flex-row gap-4 items-center">
            <label htmlFor="transactionDate" className="w-28">Transaction Date:</label>
            <input
              id="transactionDate"
              type="date"
              value={transactionDate}
              onChange={(e) => setTransactionDate(e.target.value)}
              className="border border-gray-300 px-2 py-1 rounded"
            />
          </div>
          <LoadingButton
            onClick={handleAddPaymentTransaction}
            loading={isLoadingAddPayment}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded self-start"
          >Add Payment Transaction</LoadingButton>
        </div>

        {/* --- Show Shopify Transactions --- */}
        <div className="flex flex-col gap-4 border p-4 rounded-md border-purple-500">
          <h2 className="text-lg font-semibold text-purple-700">Show Shopify Transactions</h2>
          <div className="flex flex-row gap-4 items-center">
            <label htmlFor="showTxOrderId" className="w-28">Order ID:</label>
            <input
              id="showTxOrderId"
              type="text"
              value={showTxOrderId}
              onChange={(e) => setShowTxOrderId(e.target.value)}
              className="border border-gray-300 px-2 py-1 rounded"
              placeholder="e.g., 5326898888955"
            />
          </div>
          <LoadingButton
            onClick={handleShowTransactions}
            loading={isLoadingShowTx || isLoadingGetOrder} // Disable both if either is loading
            className="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded self-start"
          >Show Transactions</LoadingButton>
          <LoadingButton
            onClick={handleGetOrder}
            loading={isLoadingGetOrder || isLoadingShowTx} // Disable both if either is loading
            className="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded self-start ml-2" // Added margin
          >Get Order</LoadingButton>

          {/* Display Transactions Result */}
          {transactionsResult && (
            <div className="mt-4 p-2 border rounded bg-gray-50 overflow-x-auto"> {/* Added overflow-x-auto */}
              <h3 className="font-semibold mb-2">Transactions Found ({transactionsResult?.length ?? 0} primary):</h3>
              {transactionsResult.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200 text-xs">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">ID</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Kind</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Created</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Processed</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Refund Amt</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Refund Date</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Voided</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Void Date</th>
                      <th className="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Gateway</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {transactionsResult.map((tx) => (
                      <tr key={tx.id}>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.id}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.kind}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.amount} {tx.currency}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.status}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.createdAt}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.processedAt}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.refundedAmount ?? 'N/A'}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.refundDate ?? 'N/A'}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.isVoided ? 'Yes' : 'No'}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.voidDate ?? 'N/A'}</td>
                        <td className="px-2 py-2 whitespace-nowrap">{tx.gateway}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-sm text-gray-500">{transactionsResult ? 'No primary transactions found or error fetching.' : 'Enter an Order ID to show transactions.'}</p>
              )}
            </div>
          )}
        </div>
        {/* Display Order Result */}
        {orderResult && (
            <div className="mt-4 p-2 border rounded bg-gray-50 overflow-x-auto">
                <h3 className="font-semibold mb-2">Order Details:</h3>
                <pre className="text-xs whitespace-pre-wrap break-all">
                    {JSON.stringify(orderResult, null, 2)}
                </pre>
            </div>
        )}

        {/* --- Void Shopify Transaction --- */}
        <div className="flex flex-col gap-4 border p-4 rounded-md border-red-500">
          <h2 className="text-lg font-semibold text-red-700">Void Shopify Transaction (Experimental)</h2>
           <p className="text-sm text-red-600">Warning: The backend `voidTransaction` function might need adjustments based on Shopify API requirements for voiding.</p>
          <div className="flex flex-row gap-4 items-center">
            <label htmlFor="voidOrderId" className="w-32">Order ID (Shopify):</label>
            <input
              id="voidOrderId"
              type="text"
              value={voidOrderId}
              onChange={(e) => setVoidOrderId(e.target.value)}
              className="border border-gray-300 px-2 py-1 rounded"
              placeholder="e.g., 5326898888955"
            />
          </div>
          <div className="flex flex-row gap-4 items-center">
            <label htmlFor="voidTxId" className="w-32">Transaction ID:</label>
            <input
              id="voidTxId"
              type="text"
              value={voidTxId}
              onChange={(e) => setVoidTxId(e.target.value)}
              className="border border-gray-300 px-2 py-1 rounded"
              placeholder="e.g., 6613530411259"
            />
          </div>
          <LoadingButton
            onClick={() => handleVoidTransaction('void')}
            loading={isLoadingVoidTx}
            className="bg-red-600 hover:bg-red-800 text-white font-bold py-2 px-4 rounded self-start"
          >Void Transaction</LoadingButton>
          <LoadingButton
            onClick={() => handleVoidTransaction('refund')}
            loading={isLoadingVoidTx}
            className="bg-red-600 hover:bg-red-800 text-white font-bold py-2 px-4 rounded self-start"
          >Refund Transaction</LoadingButton>
        </div>

        {/* --- Update Shopify Order Processed At --- */}
        <div className="flex flex-col gap-4 border p-4 rounded-md border-green-500">
          <h2 className="text-lg font-semibold text-green-700">Update Shopify Order Processed At</h2>
          <p className="text-sm text-gray-600">Enter Order ID and the desired new processedAt date. <strong className='text-orange-600'>Warning:</strong> This might have unintended effects.</p>
          <div className="flex flex-row gap-4 items-center">
            <label htmlFor="updateOrderId" className="w-36">Order ID (Number):</label>
            <input
              id="updateOrderId"
              type="text"
              value={updateOrderId}
              onChange={(e) => setUpdateOrderId(e.target.value)}
              className="border border-gray-300 px-2 py-1 rounded"
              placeholder="e.g., 1234567890"
            />
          </div>
          {/* Removed Transaction ID input field */}
          <div className="flex flex-row gap-4 items-center">
            <label htmlFor="newProcessedAtDate" className="w-36">New Processed At Date:</label>
            <input
              id="newProcessedAtDate"
              type="date"
              value={newProcessedAtDate}
              onChange={(e) => setNewProcessedAtDate(e.target.value)}
              className="border border-gray-300 px-2 py-1 rounded"
            />
          </div>
          <LoadingButton
            onClick={handleUpdateProcessedAt}
            loading={isLoadingUpdateProcessedAt}
            className="bg-green-600 hover:bg-green-800 text-white font-bold py-2 px-4 rounded self-start"
          >Update Order Processed At</LoadingButton>
        </div>

      </div>
    </main>
  );
}

