"use client";

import { useState, useEffect, useRef, useMemo, useCallback } from "react";
import CcGrid from "~/components/cc-grid";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-balham.css";
import {
  ICellRendererParams,
  CellValueChangedEvent,
  ValueFormatterParams,
  ValueGetterParams,
  ValueSetterParams,
  ColDef,
  RowValueChangedEvent
} from "ag-grid-community";
import { toast } from 'sonner';
import SelectEditor from "~/components/selectEditor";
import { Button } from "@nextui-org/react";
import QuickPrompt from "~/components/quickPrompt";
import { createMicroCenterOrder, addInvoiceToPo, updateInvoiceFromShopify, updateInvoicesFromXero } from "./poDataUtil";
import React from "react";
import ItemGridHeader from "./itemGridHeader";
import { PurchaseOrderRepository } from "~/data/purchase-orders/repository";
import type { PurchaseOrder, PurchaseOrderItem, UpdatePurchaseOrderItemData, Invoice, LineItem } from "~/data/purchase-orders/types";
import firebaseClient from "~/data/google/firebase-client";
import { fromShopifyGid } from "~/data/shopify/shopifyUtils";
import { deleteField } from "firebase/firestore";

type ItemGridProps = {
  po: PurchaseOrder;
  poId: string;
  poDate: Date;
};

const ItemGrid: React.FC<ItemGridProps> = ({ po, poId, poDate }) => {
  const [isItemDataLoading, setIsItemDataLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [itemData, setItemData] = useState<PurchaseOrderItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const itemDataRef = useRef(itemData);
  const quickPromptRef = useRef<{ open: (context: any) => void }>(null);
  const quickPromptDeleteRef = useRef<{ open: () => void }>(null);
  const itemGridRef = useRef<any>(null);
  const [products, setProducts] = useState<any[]>([]);
  const purchaseOrderRepository = new PurchaseOrderRepository(firebaseClient);

  //Item columns
  const [itemColDefs, setItemColDefs] = useState<ColDef[]>([
    { field: "id", hide: true },
    { field: "sku" },
    { field: "quantity", cellDataType: 'number' },
    { field: "amount", cellDataType: 'number' },
    { field: "total-cost", cellDataType: 'number',
      valueGetter: (params: ValueGetterParams) => {
        return Math.round(params.data?.quantity * params.data?.amount * 100) / 100;
      }
    },
  ]);

  const itemColDefsRef = useRef(itemColDefs);
  useEffect(() => {
    itemColDefsRef.current = itemColDefs;
  }, [itemColDefs]);

  const deleteInvoiceClick = () => {
    if (isProcessing) {
      toast.error('Please wait for the current operation to complete');
      return;
    }
    quickPromptDeleteRef.current?.open();
  }

  useEffect(() => {
    console.log('Current itemData:', itemData);
    itemDataRef.current = itemData;
  }, [itemData]);

  const resyncFromShopifyClick = async () => {
    if (isProcessing) {
      toast.error('Please wait for the current operation to complete');
      return;
    }
    setIsProcessing(true);
    try {
      await updateInvoiceFromShopify(poId, itemData);
      toast.success('Successfully synced with Shopify');
      await fetchData();
    } catch (error) {
      console.error('Error syncing with Shopify:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to sync with Shopify: ${errorMessage}`);
    } finally {
      setIsProcessing(false);
    }
  }

  const updateInvoicesFromXeroClick = async () => {
    if (isProcessing) {
      toast.error('Please wait for the current operation to complete');
      return;
    }
    setIsProcessing(true);
    try {
      await updateInvoicesFromXero(poId);
      toast.success('Successfully updated from Xero');
      await fetchData();
    } catch (error) {
      console.error('Error updating from Xero:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to update from Xero: ${errorMessage}`);
    } finally {
      setIsProcessing(false);
    }
  }

  const saveItemToDb = async (poId: string, item: UpdatePurchaseOrderItemData, itemId: string) => {
    if (!itemId) throw new Error('Item ID is required');
    try {
      await purchaseOrderRepository.updateItem(poId, itemId as any, item);
      toast.success('Item saved successfully');
    } catch (error) {
      console.error('Error saving item:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to save item: ${errorMessage}`);
    }
  };

  const cleanupCorruptedInvoices = async (poId: string) => {
    try {
      const currentPo = await purchaseOrderRepository.get(poId);
      if (!currentPo?.invoices) return;

      const corruptedInvoices = Object.entries(currentPo.invoices)
        .filter(([id, invoice]) => invoice && typeof invoice === 'object' && '_methodName' in invoice);

      if (corruptedInvoices.length > 0) {
        console.log(`Found ${corruptedInvoices.length} corrupted invoices, cleaning up...`);

        for (const [invoiceId, corruptedInvoice] of corruptedInvoices) {
          // Delete the corrupted entry
          await purchaseOrderRepository.update(poId, {
            [`invoices.${invoiceId}`]: deleteField()
          });
        }

        toast.success(`Cleaned up ${corruptedInvoices.length} corrupted invoice entries`);
      }
    } catch (error) {
      console.error('Error cleaning up corrupted invoices:', error);
      toast.error('Failed to clean up corrupted invoices');
    }
  };

  const renameInvoice = async (poId: string, oldId: string, newId: string) => {
    if (isProcessing) {
      toast.error('Please wait for the current operation to complete');
      return;
    }
    setIsProcessing(true);
    try {
      if (!oldId || !newId) {
        throw new Error('Both old and new invoice IDs are required');
      }

      // First, get the current PO to access the invoice summary
      const currentPo = await purchaseOrderRepository.get(poId);
      if (!currentPo?.invoices?.[oldId]) {
        throw new Error(`Invoice ${oldId} not found in PO`);
      }

      // Update PO-level invoice data (rename the key and update invoiceNumber)
      const oldInvoiceSummary = currentPo.invoices[oldId];

      // First, delete the old invoice key
      await purchaseOrderRepository.update(poId, {
        [`invoices.${oldId}`]: deleteField()
      });

      // Then, add the new invoice key with updated invoiceNumber
      await purchaseOrderRepository.update(poId, {
        [`invoices.${newId}`]: {
          ...oldInvoiceSummary,
          invoiceNumber: newId
        }
      });

      //Rename column
      setItemColDefs(currentColDefs => currentColDefs.map(col => {
        if (col.field === oldId) {
          return { ...col, field: newId, headerName: newId };
        }
        return col;
      }));

      //Rename invoice in data
      setItemData(currentData => currentData.map(item => {
        if (item.invoices?.[oldId]) {
          const updatedInvoices = { ...item.invoices };
          updatedInvoices[newId] = {
            ...updatedInvoices[oldId], // Preserve ALL existing fields
            id: newId,                 // Update only the id
            invoiceNumber: newId       // Update only the invoiceNumber
          };
          delete updatedInvoices[oldId];
          const updatedItem = { ...item, invoices: updatedInvoices };
          if (item.id) {
            saveItemToDb(poId, updatedItem, item.id);
          }
          return updatedItem;
        }
        return item;
      }));
      toast.success(`Invoice renamed from ${oldId} to ${newId}`);
    } catch (error) {
      console.error('Error renaming invoice:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to rename invoice: ${errorMessage}`);
      // Attempt to recover by refreshing data
      await fetchData();
    } finally {
      setIsProcessing(false);
    }
  };

  async function onSendPDFClick(invoiceId: string) {
    if (isProcessing) {
      toast.error('Please wait for the current operation to complete');
      return;
    }
    setIsProcessing(true);
    try {
      if (!invoiceId) throw new Error('Invoice ID is required');
      let invoiceData = po.invoices[invoiceId];
      if (!invoiceData) throw new Error('Invoice data not found');

      let shopId = invoiceData.shopId && fromShopifyGid(invoiceData.shopId);
      if (!shopId) throw new Error('Shop ID not found');

      const urlPath = `https://cookiecad-utils.api.cookiecad.com/generateinvoice/${shopId}`;
      const response = await fetch(urlPath, {
        method: "POST",
        headers: {
          'Accept': 'application/pdf'
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      if (blob.size === 0) {
        throw new Error("Received an empty file");
      }

      const contentDisposition = response.headers.get('content-disposition');
      const filenameMatch = contentDisposition && contentDisposition.match(/filename="(.+)"/);
      const filename = filenameMatch ? filenameMatch[1] : `invoice-${invoiceId}.pdf`;

      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);

      toast.success('PDF generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to generate PDF: ${errorMessage}`);
    } finally {
      setIsProcessing(false);
    }
  }

  const invoiceColDef = (invoiceId: string, handleCreateMicroCenterOrder: any) => ({
    field: invoiceId,
    headerName: invoiceId,
    cellDataType: 'number',
    valueGetter: (params: ValueGetterParams) => {
      return params.node?.isRowPinned()
        ? params.colDef.field && params.data[params.colDef.field]
        : params.data?.invoices?.[invoiceId]?.quantity
    },
    valueSetter: (params: ValueSetterParams) => {
      try {
        const invoice: Invoice = {
          id: invoiceId,
          invoiceNumber: invoiceId,
          amount: 0,
          date: new Date(),
          status: 'PENDING',
          quantity: params.newValue
        };

        if (params.data.invoices) {
          params.data.invoices[invoiceId] = invoice;
        } else {
          params.data.invoices = { [invoiceId]: invoice };
        }
        return true;
      } catch (error) {
        console.error('Error setting invoice value:', error);
        return false;
      }
    },
    headerComponent: (props: any) => ItemGridHeader({...props,
      onRename: () => {
        if (isProcessing) {
          toast.error('Please wait for the current operation to complete');
          return;
        }
        quickPromptRef.current?.open({
          action: "rename",
          oldId: props.column.getColDef().field,
        })
      },
      onSendPdf: () => {
        onSendPDFClick(props.column.getColDef().field!);
      },
      onCreateShopOrder: () => {
        if (isProcessing) {
          toast.error('Please wait for the current operation to complete');
          return;
        }
        console.log("createShopOrder", itemDataRef.current);
        handleCreateMicroCenterOrder(props.column.getColDef().field!);
      }
    }),
  });

  const addInvoiceColumn = ((id: string) => {
    console.log("addInvoiceColumn", id)
    if (itemColDefsRef.current.some((col) => col.field === id)) {
      return;
    }
    const newColDefs = [
      ...itemColDefsRef.current,
      invoiceColDef(id, handleCreateMicroCenterOrder),
    ];
    itemColDefsRef.current = newColDefs;
    setItemColDefs(newColDefs);
  });

  const createInvoice = ((id: string) => {
    if (isProcessing) {
      toast.error('Please wait for the current operation to complete');
      return;
    }
    setIsProcessing(true);
    try {
      if (!id) throw new Error('Invoice ID is required');

      addInvoiceColumn(id);
      setItemData(currentData => currentData.map((item) => {
        const invoices = item.invoices || {};
        const invoicedQuantity = Object.values(invoices).reduce((acc, inv) =>
          acc + (inv.quantity || 0), 0);

        const invoice: Invoice = {
          id,
          invoiceNumber: id,
          amount: 0,
          date: new Date(),
          status: 'PENDING',
          quantity: item.quantity - invoicedQuantity
        };

        const newItem = {
          ...item,
          invoices: {
            ...invoices,
            [id]: invoice
          },
        };

        if (item.id) {
          saveItemToDb(poId, newItem, item.id);
        }
        return newItem;
      }));
      toast.success('Invoice created successfully');
    } catch (error) {
      console.error('Error creating invoice:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to create invoice: ${errorMessage}`);
      // Attempt to recover by refreshing data
      fetchData();
    } finally {
      setIsProcessing(false);
    }
  });

  const deleteInvoice = async (poId: string, id: string) => {
    if (isProcessing) {
      toast.error('Please wait for the current operation to complete');
      return;
    }
    setIsProcessing(true);
    try {
      if (!id) throw new Error('Invoice ID is required');

      setItemColDefs(currentColDefs => currentColDefs.filter(col => col.field !== id));

      setItemData(currentData => currentData.map(item => {
        if (item.invoices?.[id]) {
          const updatedInvoices = { ...item.invoices };
          delete updatedInvoices[id];
          const updatedItem = { ...item, invoices: updatedInvoices };
          if (item.id) {
            saveItemToDb(poId, updatedItem, item.id);
          }
          return updatedItem;
        }
        return item;
      }));
      toast.success('Invoice deleted successfully');
    } catch (error) {
      console.error('Error deleting invoice:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to delete invoice: ${errorMessage}`);
      // Attempt to recover by refreshing data
      await fetchData();
    } finally {
      setIsProcessing(false);
    }
  };

const handleCreateMicroCenterOrder = useCallback(async (id: string) => {
  if (isProcessing) {
    toast.error('Please wait for the current operation to complete');
    return;
  }
  setIsProcessing(true);
  try {
    if (!id) throw new Error('Invoice ID is required');

    console.log("createMicroCenterOrder", id);
    console.log("itemData", itemDataRef.current);
    const invoiceItems: LineItem[] = [];

    itemDataRef.current.forEach(item => {
      if (item.invoices?.[id] && item.product?.shopify?.variant?.id) {
        const quantity = item.invoices[id].quantity;
        if (quantity && quantity > 0) {
          invoiceItems.push({
            variant_id: item.product.shopify.variant.id,
            quantity: quantity,
            discount: item.product.shopify.variant.price - item.amount
          });
        }
      }
    });

    if (invoiceItems.length === 0) {
      throw new Error('No valid items found for order creation');
    }

    console.log("lineItems", invoiceItems);
    let order = await createMicroCenterOrder(invoiceItems, poId, poDate);
    console.log("createMicroCenterOrder result", order);

    if (!order || !order.name) {
      throw new Error('Failed to create order: Invalid response');
    }

    let orderName = order.name.replace("#", "");
    if (order.id) {
      await renameInvoice(poId, id, orderName);
      await addInvoiceToPo(poId, orderName, {
        invoiceNumber: orderName,
        shopId: order.id
      });
    }

    await fetchData();
    toast.success('Micro Center order created successfully');
  } catch (error) {
    console.error('Error creating Micro Center order:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    toast.error(`Failed to create Micro Center order: ${errorMessage}`);
  } finally {
    setIsProcessing(false);
  }
}, [poId, poDate, renameInvoice, isProcessing]);

  const fetchData = async () => {
    setIsItemDataLoading(true);
    setError(null);
    console.log(`Fetching purchase-order-items for ${poId}`);
    try {
      // Clean up any corrupted invoice data first
      await cleanupCorruptedInvoices(poId);

      const data = await purchaseOrderRepository.getItems(poId);
      console.log({ "purchase-order-items": data });

      if (!data || data.length === 0) {
        setError('No items found for this purchase order');
        return;
      }

      for (const item of data) {
        if (item.invoices) {
          for (const invoiceId of Object.keys(item.invoices)) {
            addInvoiceColumn(invoiceId);
          }
        }
      }
      setItemData(data);
    } catch (error) {
      console.error('Error fetching items:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(`Failed to fetch items: ${errorMessage}`);
      toast.error(`Failed to fetch items: ${errorMessage}`);
    } finally {
      setIsItemDataLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [poId]);

  const defaultColDef = useMemo<ColDef>(() => {
    return {
      editable: true,
    };
  }, []);

  useEffect(() => {
    if (products.length === 0) {
      console.log("Fetching products");
      fetch("../api/products")
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          let products = data.products || [];
          console.log("products", products);
          setProducts(products);
        })
        .catch((error) => {
          console.error('Error fetching products:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          toast.error(`Failed to fetch products: ${errorMessage}`);
        });
    }
  }, [products]);

  useEffect(() => {
    if (!isItemDataLoading && products.length > 0) {
      console.log("assigning products", products);
      setItemData(itemData => {
        return itemData.map((item) => {
          let matchingProduct = products.find((product: any) => product.sku === item.sku);
          if (matchingProduct) {
            return { ...item, product: matchingProduct };
          }
          return item;
        });
      });

      setItemColDefs(itemColDefs => {
        let columnDefs = itemColDefs.map((i) => {
          if (i.field === "sku") {
            return {
              ...i,
              editable: true,
              cellEditor: SelectEditor,
              cellEditorParams: {
                options: products.map((i: any) => ({value: i.sku, label: i.sku})),
              },
            };
          }
          return i;
        });
        console.log({columnDefs});
        return columnDefs;
      });
    }
  }, [poId, products, isItemDataLoading]);

  useEffect(() => {
    console.log("itemColDefs", itemColDefs);
  }, [itemColDefs]);

  async function onCellValueChangedItems(params: CellValueChangedEvent) {
    try {
      console.log("onCellValueChangedItems", params);
      let additionalItemData = {};
      if (params.column.getColId() === 'sku') {
        const matchingProduct = products.find(product => product?.sku === params.newValue);
        if (matchingProduct) {
          additionalItemData = { product: matchingProduct };
        }
      }

      // For new rows, create a new item
      if (!params.data.id) {
        console.log("Creating new item with SKU:", params.newValue);
        const newItem = {
          sku: params.newValue,
          quantity: 0,
          amount: 0,
          ...additionalItemData
        };
        try {
          await purchaseOrderRepository.addItem(poId, newItem);
          // Refresh all data to ensure we have the latest state
          await fetchData();
          toast.success('New item created successfully');
        } catch (error) {
          console.error('Error creating new item:', error);
          throw error;
        }
      } else {
        // Update existing item
        setItemData(currentData => currentData.map(item => {
          if (item.id === params.data.id) {
            return {...item, [params.column.getColId()]: params.newValue, ...additionalItemData};
          }
          return item;
        }));

        await saveItemToDb(poId, {...params.data, ...additionalItemData}, params.data.id);
        toast.success('Item updated successfully');
      }
    } catch (error) {
      console.error('Error updating item:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to update item: ${errorMessage}`);
      // Attempt to recover by refreshing the data
      await fetchData();
    }
  }

  async function handleRowDelete(params: any) {
    if (isProcessing) {
      toast.error('Please wait for the current operation to complete');
      return;
    }
    setIsProcessing(true);
    try {
      if (!params.id) throw new Error('Row ID is required');

      await purchaseOrderRepository.deleteItem(poId, params.id);
      toast.success('Row deleted successfully');
      await fetchData();
    } catch (error) {
      console.error('Error deleting row:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to delete row: ${errorMessage}`);
      // Attempt to recover by refreshing data
      await fetchData();
    } finally {
      setIsProcessing(false);
    }
  }

  if (isItemDataLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 text-center">
          <h2 className="text-xl font-bold mb-4">Error</h2>
          <p>{error}</p>
          <button
            onClick={fetchData}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <CcGrid
        ref={itemGridRef}
        rowData={itemData}
        colDefs={itemColDefs}
        setColDefs={setItemColDefs}
        autoAddMissingColumns={false}
        onRowDelete={handleRowDelete}
        additionalOptions = {{
          enableCellTextSelection:true,
          defaultColDef,
          onCellValueChanged:onCellValueChangedItems
        }}
        headerItems={
          <>
            <Button
              onClick={() => {
                if (isProcessing) {
                  toast.error('Please wait for the current operation to complete');
                  return;
                }
                console.log("create", quickPromptRef.current);
                quickPromptRef.current?.open({action: 'create'});
              }}
              disabled={isProcessing}
            >
              Add Invoice
            </Button>
            <Button
              onClick={deleteInvoiceClick}
              disabled={isProcessing}
            >
              Delete Invoice
            </Button>
            <Button
              onClick={resyncFromShopifyClick}
              disabled={isProcessing}
            >
              Resync from Shopify
            </Button>
            <Button
              onClick={updateInvoicesFromXeroClick}
              disabled={isProcessing}
            >
              Update from/to Xero
            </Button>
          </>
        }
      />
      <QuickPrompt
        ref={quickPromptRef}
        message="Enter the invoice id"
        title="Add Invoice Column"
        onConfirm={(id: string, context: any) => {
          if (context.action == 'create') {
            createInvoice(id);
          }
          else {
            renameInvoice(poId, context.oldId, id);
          }
        }}/>
      <QuickPrompt
        ref={quickPromptDeleteRef}
        message="Enter the invoice id"
        title="Delete Invoice"
        onConfirm={(id: string) => {
          deleteInvoice(poId, id);
        }}/>
      <div className="status-bar">
        <p>Total Row Count: {itemData.length}</p>
      </div>
    </>
  );
};

ItemGrid.displayName = 'ItemGrid';
export default ItemGrid;
