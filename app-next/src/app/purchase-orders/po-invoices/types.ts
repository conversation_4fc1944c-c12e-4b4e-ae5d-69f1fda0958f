import { InvoiceSummary } from "~/data/purchase-orders/types";

export type XeroLineItem = {
  lineItemID?: string;
  description?: string;
  lineAmount?: number;
  accountCode?: string;
  invoiceNumber?: string; // Added for storing reference invoice numbers
};

export type XeroInvoice = {
  invoiceNumber?: string;
  lineItems: XeroLineItem[];
};

export type FlattenedInvoice = InvoiceSummary & {
  poId: string;
  poNumber: string;
  poDate: Date;
  poStatus: string;
  lineItems?: XeroLineItem[];
  creditNotes?: XeroInvoice[];
};