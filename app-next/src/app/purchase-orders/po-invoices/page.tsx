"use client";

import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import Cc<PERSON>rid from "~/components/cc-grid";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-balham.css";
import {
  CellValueChangedEvent,
  ValueFormatterParams,
  ValueSetterParams,
  ColDef,
  GridApi,
  GridReadyEvent
} from "ag-grid-community";
import firebaseClient from "~/data/google/firebase-client";
import { Checkbox, Button } from "@nextui-org/react";
import { toast } from 'sonner';
import DepositForm from "./deposit-form";
import { FlattenedInvoice } from "./types";
import {
  createDeposit,
  getXeroInvoices,
  syncXeroStatusToShopify,
  updateAllInvoicesShopStatus,
  markAllShopifyOrdersPaid,
  syncXeroAndFirestore,
  updateShopifyPoNumbers
} from "./actions";
import { LineItem as XeroLineItem } from "xero-node";

// Extended LineItem type to include invoiceNumber
interface LineItem extends XeroLineItem {
  invoiceNumber?: string;
}
import { PurchaseOrderRepository } from '~/data/purchase-orders/repository';

interface XeroError {
  message: string;
  type?: string;
  details?: unknown;
}

const formatErrorMessage = (error: XeroError | undefined) => {
  if (!error) return 'An unknown error occurred';

  let message = error.message;
  if (error.type) {
    message += ` (${error.type})`;
  }
  if (typeof error.details === 'string') {
    message += `\n\nDetails:\n${error.details}`;
  }
  return message;
};

const purchaseOrderRepository = new PurchaseOrderRepository(firebaseClient);

export default function PoInvoicesPage() {
  const [rowData, setRowData] = useState<FlattenedInvoice[]>([]);
  const gridRef = useRef<any>(null);
  const [showArchivedAndPaid, setShowArchivedAndPaid] = useState(false);
  const [isDepositFormOpen, setIsDepositFormOpen] = useState(false);
  const [selectedInvoices, setSelectedInvoices] = useState<FlattenedInvoice[]>([]);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadData = useCallback(async () => {
    try {
      const data = showArchivedAndPaid
        ? await purchaseOrderRepository.getAll()
        : await purchaseOrderRepository.getNonArchived();

      const flattenedData: FlattenedInvoice[] = data.reduce((acc: FlattenedInvoice[], po) => {
        const poInvoices = Object.entries(po.invoices|| {}).map(([invoiceId, invoice]) => ({
          id: invoiceId,
          poId: po['po-number'],
          poNumber: po["po-number"],
          poDate: po["po-date"],
          invoiceNumber: invoice.invoiceNumber,
          status: invoice.status || '',
          paid: invoice.paid,
          paidDate: invoice.paidDate,
          shopId: invoice.shopId,
          shopStatus: invoice.shopStatus,
        } as FlattenedInvoice
      ));
        return [...acc, ...poInvoices];
      }, []);

      // Filter out invoices that are paid in both Xero and Shopify (unless showArchivedAndPaid is true)
      const filteredData = showArchivedAndPaid
        ? flattenedData
        : flattenedData.filter(invoice =>
            !(invoice.status === 'PAID' && invoice.shopStatus === 'PAID')
          );

      setRowData(filteredData);
      toast.success(`${showArchivedAndPaid ? 'All' : 'Active'} invoices loaded successfully`);
    } catch (error) {
      console.error('Error loading invoices:', error);
      toast.error('Failed to load invoices: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }, [showArchivedAndPaid]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const [colDefs, setColDefs] = useState<ColDef[]>([
    {
      checkboxSelection: true,
      headerCheckboxSelection: true,
      width: 40,
      pinned: 'left'
    },
    { field: "id", hide: true },
    { field: "poId", hide: true },
    { field: "poNumber", headerName: "PO Number" },
    {
      field: "poDate",
      headerName: "PO Date",
      cellDataType: 'date',
      cellEditor: 'agDateCellEditor'
    },
    { field: "invoiceNumber", headerName: "Invoice Number" },
    {
      field: "status",
      headerName: "Invoice Status",
      cellEditor: 'agSelectCellEditor',
      cellEditorParams: {
        values: ['New', 'Invoice Sent', 'Completed', 'PAID']
      }
    },
    {
      field: "paid",
      headerName: "Paid Amount",
      cellDataType: 'number',
    },
    {
      field: "paidDate",
      headerName: "Paid Date",
      cellDataType: 'date',
      cellEditor: 'agDateCellEditor'
    },
    { field: "shopId", headerName: "Shop ID" },
    { field: "shopStatus", headerName: "Shop Status" },
  ]);

  const defaultColDef = useMemo<ColDef>(() => {
    return {
      editable: true,
      sortable: true,
      filter: true,
    };
  }, []);

  const onCellValueChanged = async (event: CellValueChangedEvent) => {
    const { data } = event;
    if (!data.poId) return;

    try {
      // Update the invoice data in the PO document
      const updateData = {
        [`invoices.${data.id}`]: {
          invoiceNumber: data.invoiceNumber,
          status: data.status,
          paid: data.paid,
          paidDate: data.paidDate,
          shopId: data.shopId,
          shopStatus: data.shopStatus,
        }
      };

      await purchaseOrderRepository.update(data.poId, updateData);
      toast.success('Invoice updated successfully');
    } catch (error) {
      console.error('Error updating invoice:', error);
      toast.error('Failed to update invoice: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };

  const handleDepositClick = async () => {
    if (!gridApi) return;

    const selectedRows = gridApi.getSelectedRows() as FlattenedInvoice[];
    if (selectedRows.length === 0) {
      toast.error('Please select at least one invoice');
      return;
    }

    setIsLoading(true);
    try {
      // Fetch full invoice data from Xero for each selected invoice
      const result = await getXeroInvoices(selectedRows);
      if (!result.success) {
        throw new Error(formatErrorMessage(result.error));
      }

      // Update the selected invoices with the Xero data
      const updatedInvoices = selectedRows.map((invoice, index) => ({
        ...invoice,
        lineItems: result.invoices?.[index]?.lineItems || []
      }));

      setSelectedInvoices(updatedInvoices);
      setIsDepositFormOpen(true);
    } catch (error) {
      console.error('Error fetching invoice data:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to fetch invoice data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDepositConfirm = async (
    deletedLineItems: Record<string, LineItem[]>,
    creditNotes: Record<string, LineItem[]>,
    payments: Record<string, LineItem[]>,
    bankAccountId: string
  ): Promise<void> => {
    try {
      // Process the invoices with deletions, credit notes, and payments
      const result = await createDeposit(selectedInvoices, deletedLineItems, creditNotes, payments, bankAccountId);

      if (!result.success) {
        console.log("Error Result", result)
        setIsDepositFormOpen(true);
        throw new Error(formatErrorMessage(result.error));
      }

      toast.success('Deposit created successfully');
      setIsDepositFormOpen(false);

      // TODO: Do this in createDeposit

      // Update invoice statuses based on the returned statuses
      // for (const invoice of selectedInvoices) {
      //   const updatedStatus = result.updatedStatuses?.find(
      //     (status: { invoiceNumber: string; status: string }) =>
      //       status.invoiceNumber === invoice.invoiceNumber
      //   );
      //   console.log("invoice", invoice, "updatedStatus", updatedStatus)

      //   if (updatedStatus?.status) {
      //     const updateData = {
      //       [`invoices.${invoice.id}.status`]: updatedStatus.status,
      //       [`invoices.${invoice.id}.paidDate`]: new Date()
      //     };
      //     await purchaseOrderRepository.update(invoice.poId, updateData);
      //   }
      // }

      // Refresh the grid data
      await loadData();

    } catch (error) {
      console.error('Error creating deposit:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create deposit');
    }
  };

  const handleSyncXeroToShopify = async () => {
    setIsLoading(true);
    try {
      const result = await syncXeroStatusToShopify(showArchivedAndPaid);

      if (!result.success) {
        throw new Error(result.error?.message || 'Unknown error occurred');
      }

      toast.success('Successfully synced Xero status to Shopify');
      await loadData(); // Refresh the data
    } catch (error) {
      console.error('Error syncing Xero to Shopify:', error);
      toast.error(`Failed to sync: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateShopifyStatus = async () => {
    setIsLoading(true);
    try {
      const result = await updateAllInvoicesShopStatus();

      if (!result.success) {
        throw new Error(result.error?.message || 'Unknown error occurred');
      }

      toast.success('Shopify paid status updated successfully');
      await loadData(); // Refresh the data
    } catch (error) {
      console.error('Error updating Shopify status:', error);
      toast.error(`Failed to update Shopify status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkShopifyOrdersPaid = async () => {
    setIsLoading(true);
    try {
      const result = await markAllShopifyOrdersPaid();

      if (!result.success) {
        throw new Error(result.error?.message || 'Unknown error occurred');
      }

      toast.success('Shopify orders marked as paid successfully');
      await loadData(); // Refresh the data
    } catch (error) {
      console.error('Error marking Shopify orders as paid:', error);
      toast.error(`Failed to mark Shopify orders as paid: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncXeroAndFirestore = async () => {
    setIsLoading(true);
    try {
      const result = await syncXeroAndFirestore();

      if (!result.success) {
        throw new Error(result.error?.message || 'Unknown error occurred');
      }

      toast.success('Xero and Firestore synced successfully');
      await loadData(); // Refresh the data
    } catch (error) {
      console.error('Error syncing Xero and Firestore:', error);
      toast.error(`Failed to sync Xero and Firestore: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateShopifyPoNumbers = async () => {
    setIsLoading(true);
    try {
      const result = await updateShopifyPoNumbers();

      if (!result.success) {
        throw new Error(result.error?.message || 'Unknown error occurred');
      }

      toast.success('Shopify PO numbers updated successfully');
      await loadData(); // Refresh the data
    } catch (error) {
      console.error('Error updating Shopify PO numbers:', error);
      toast.error(`Failed to update Shopify PO numbers: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main className="flex flex-1 flex-col items-center">
      <h1 className="mb-10 text-xl">Purchase Order Invoices</h1>

      <div style={{ width: "100%" }}>
        <div className="flex flex-1 w-full mt-10" style={{ height: 'calc(100vh - 200px)' }}>
          <CcGrid
            ref={gridRef}
            rowData={rowData}
            colDefs={colDefs}
            setColDefs={setColDefs}
            additionalOptions={{
              enableCellTextSelection: true,
              defaultColDef,
              onCellValueChanged,
              rowSelection: 'multiple',
              onGridReady
            }}
            headerItems={
              <div className="flex items-center gap-4">
                <Checkbox
                  checked={showArchivedAndPaid}
                  onChange={(e) => setShowArchivedAndPaid(e.target.checked)}
                >
                  Show Archived and Paid
                </Checkbox>
                <Button
                  color="primary"
                  onClick={handleDepositClick}
                  isLoading={isLoading}
                >
                  Deposit Check
                </Button>
                <Button
                  color="secondary"
                  onClick={handleSyncXeroToShopify}
                  isLoading={isLoading}
                >
                  Sync Xero Status to Shopify
                </Button>
                <Button
                  color="default"
                  onClick={handleUpdateShopifyStatus}
                  isLoading={isLoading}
                >
                  Import Shopify Paid Status
                </Button>
                <Button
                  color="default"
                  onClick={handleMarkShopifyOrdersPaid}
                  isLoading={isLoading}
                >
                  Mark Shopify Orders as Paid
                </Button>
                <Button
                  color="default"
                  onClick={handleSyncXeroAndFirestore}
                  isLoading={isLoading}
                >
                  Sync Xero and Firestore
                </Button>
                <Button
                  color="default"
                  onClick={handleUpdateShopifyPoNumbers}
                  isLoading={isLoading}
                >
                  Update Shopify PO Numbers
                </Button>
              </div>
            }
          />
        </div>

        <div className="status-bar">
          <p>Total Row Count: {rowData.length}</p>
        </div>
      </div>

      <DepositForm
        isOpen={isDepositFormOpen}
        onClose={() => setIsDepositFormOpen(false)}
        selectedInvoices={selectedInvoices}
        onConfirm={handleDepositConfirm}
      />
    </main>
  );
}
