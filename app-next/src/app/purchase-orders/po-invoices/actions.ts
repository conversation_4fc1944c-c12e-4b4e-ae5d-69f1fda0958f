"use server";

import { Xero, cookiecadAccounts, cookiecadContacts, AccountType, XeroAccount, AccountStatus, invoiceStatusEnum } from "~/data/xero/xero";
import { BatchPayment, Invoice, LineItem as XeroLineItem, CreditNote, Account } from "xero-node";

// Extended LineItem type to include invoiceNumber
interface LineItem extends XeroLineItem {
  invoiceNumber?: string;
}
import { FlattenedInvoice } from "./types";
import { updateInvoicesFromXero } from '~/app/purchase-orders/[poId]/poDataUtil';
import { markShopifyOrderPaid, updateInvoiceShopStatus } from '~/data/xero/shopifyToXero';
import { PurchaseOrderRepository } from "~/data/purchase-orders/repository";
import firebase from "~/data/google/firebase-server";
import * as shopifyGraphQl from "~/data/shopify/shopifyGrapql";

// Default bank account as fallback
const DEFAULT_BANK_ACCOUNT_ID = cookiecadAccounts.blueVine;
const CREDIT_NODE_ACCOUNT_CODE = cookiecadAccounts.microCenterVendorReimbursement;

interface DepositResult {
  success: boolean;
  error?: {
    message: string;
    type?: string;
    details?: unknown;
  };
}

// Helper to serialize Xero error response
const serializeXeroError = (error: any) => {
  const errorBody = error.body || error.response?.body;
  if (!errorBody) return { error: { message: error.message || 'Unknown error' } };

  const details = (errorBody.message || errorBody.validation)
  ? {
      errorNumber: errorBody.ErrorNumber,
      elements: errorBody.Elements,
      validation: JSON.stringify(errorBody.Elements, null, 2)
    }
  : JSON.stringify(errorBody, null, 2);
  return {
    error: {
      message: errorBody.Message || 'Xero API error',
      type: errorBody.Type,
      details
    }
  };
};

// Helper to serialize Xero invoice data
const serializeXeroInvoice = (invoice: Invoice) => ({
  invoiceNumber: invoice.invoiceNumber,
  lineItems: invoice.lineItems?.map(line => ({
    lineItemID: line.lineItemID,
    description: line.description,
    lineAmount: line.lineAmount,
    accountCode: line.accountCode
  })) || []
});

export async function getXeroInvoices(selectedInvoices: FlattenedInvoice[]): Promise<{
  success: boolean;
  error?: {
    message: string;
    type?: string;
    details?: unknown;
  };
  invoices?: ReturnType<typeof serializeXeroInvoice>[];
}> {
  try {
    const xero = new Xero();
    await xero.init();

    // Get all invoices in a single query
    const invoiceNumbers = selectedInvoices.map(invoice => invoice.invoiceNumber);
    const fullInvoices = await xero.getInvoicesFromInvoiceNumbers(invoiceNumbers);

    // Serialize the invoices
    const invoices = fullInvoices.map(invoice => serializeXeroInvoice(invoice));

    return {
      success: true,
      invoices
    };
  } catch (error) {
    console.error('Error fetching Xero invoices:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}

export async function getBankAccounts(): Promise<{
  success: boolean;
  error?: {
    message: string;
    type?: string;
    details?: unknown;
  };
  accounts?: Array<{accountID: string, name: string}>;
}> {
  try {
    const xero = new Xero();
    await xero.init();

    // Get bank accounts
    // Retrieve all accounts from Xero
    const allAccounts = await xero.getAccounts();

    // console.log("All accounts:", allAccounts);
    // Filter to only active bank accounts
    const bankAccounts = allAccounts.filter(account =>
      account.type === AccountType.BANK &&
      account.status === Account.StatusEnum.ACTIVE
    );

    // Get the full account details for each bank account to get the accountID

    return {
      success: true,
      accounts: bankAccounts
    };
  } catch (error) {
    console.error('Error fetching bank accounts:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}

// Map of friendly account codes to accountIDs for known accounts
const ACCOUNT_ID_MAP: Record<string, string> = {
  "1060": cookiecadAccounts.blueVine, // BluVine
  "1200": cookiecadAccounts.brex,     // Brex
};

export async function createDeposit(
  selectedInvoices: FlattenedInvoice[],
  deletedLineItems: Record<string, LineItem[]>,
  creditNotes: Record<string, LineItem[]>,
  payments: Record<string, LineItem[]>,
  bankAccountId?: string
): Promise<DepositResult> {
  try {
    const xero = new Xero();
    await xero.init();

    // Process payments first - create new invoices for positive payments and new bills for negative payments
    const positivePayments: Record<string, LineItem[]> = {};
    const negativePayments: Record<string, LineItem[]> = {};

    // Separate positive and negative payments
    for (const invoiceId in payments) {
      const paymentItems = payments[invoiceId] || [];

      for (const item of paymentItems) {
        if (!item.invoiceNumber) {
          throw new Error("Payment item is missing invoice number");
        }

        if (item.lineAmount! >= 0) {
          // Positive payment - create a new invoice
          if (!positivePayments[item.invoiceNumber]) {
            positivePayments[item.invoiceNumber] = [];
          }
          positivePayments[item.invoiceNumber].push({
            description: item.description || "",
            lineAmount: item.lineAmount!,
            accountCode: item.accountCode
          });
        } else {
          // Negative payment - create a new bill
          if (!negativePayments[item.invoiceNumber]) {
            negativePayments[item.invoiceNumber] = [];
          }
          negativePayments[item.invoiceNumber].push({
            description: item.description || "",
            lineAmount: Math.abs(item.lineAmount!), // Make positive for bill
            accountCode: item.accountCode
          });
        }
      }
    }

    // Array to store the IDs of newly created invoices
    const createdInvoiceIds: Array<{invoiceId: string, amount: number}> = [];

    // Create new invoices for positive payments
    for (const invoiceNumber in positivePayments) {
      const items = positivePayments[invoiceNumber];
      // Calculate total amount for this invoice
      const totalAmount = items.reduce((sum, item) => sum + (item.lineAmount || 0), 0);

      const invoiceInput = {
        invoiceNumber: invoiceNumber,
        date: new Date(),
        dueDate: new Date(new Date().setDate(new Date().getDate() + 30)), // Due in 30 days
        updatedDateUTC: new Date(),
        lineItems: items.map(item => ({
          description: item.description || "",
          lineAmount: item.lineAmount!,
          accountCode: item.accountCode || cookiecadAccounts.shopRevenue // Default to shop revenue account
        })),
        contact: cookiecadContacts.generalContact
      };

      // Create the invoice and get the response
      let response: Awaited<ReturnType<typeof xero.createInvoice>>;
      try {
        response = await xero.createInvoice(invoiceInput);
      } catch (error) {
        if (error instanceof Error) {
          error.message = `Error creating invoice: ${error.message}`;
        }
        console.error("Error creating invoice:", error);
        throw error;
      }

      // Store the invoice ID for inclusion in the batch payment
      if (response?.body?.invoices?.[0]?.invoiceID) {
        createdInvoiceIds.push({
          invoiceId: response.body.invoices[0].invoiceID,
          amount: totalAmount
        });

        // Ensure the invoice is AUTHORISED
        if (response.body.invoices[0].status === Invoice.StatusEnum.DRAFT) {
          await xero.updateBillStatus(response.body.invoices[0].invoiceID, Invoice.StatusEnum.AUTHORISED);
        }
      }
    }

    // Create credit notes for negative payments instead of bills
    for (const invoiceNumber in negativePayments) {
      const items = negativePayments[invoiceNumber];

      // Process each negative payment as a credit note
      for (const item of items) {
        if (!item.invoiceNumber) {
          console.warn("Skipping item without invoice number");
          continue;
        }

        const uniqueCreditNoteNumber = `CN-${item.invoiceNumber}-${Date.now().toString().slice(-6)}`;

        // Create the credit note
        try {
          const creditNoteResponse = await xero.createCreditNote({
            type: CreditNote.TypeEnum.ACCRECCREDIT,
            contact: cookiecadContacts.generalContact,
            date: new Date().toISOString().split('T')[0],
            creditNoteNumber: uniqueCreditNoteNumber,
            lineItems: [{
              description: item.description || `Credit Note for ${item.invoiceNumber}`,
              quantity: 1,
              unitAmount: Math.abs(item.lineAmount!), // Make sure it's positive
              accountCode: item.accountCode || cookiecadAccounts.shopRevenue // Use the selected account code
            }]
          });

          // If credit note was created successfully, add it to the list
          if (creditNoteResponse?.creditNotes?.[0]?.creditNoteID) {
            // We don't need to add this to the batch payment since credit notes are handled differently
            console.log(`Created credit note ${uniqueCreditNoteNumber} with ID ${creditNoteResponse.creditNotes[0].creditNoteID}`);
          }
        } catch (error) {
          if (error instanceof Error) {
            error.message = `Error creating credit note: ${error.message}`;
          }
          console.error("Error creating credit note:", error);
          throw error;
        }
      }
    }

    // Get all invoices in a single query
    const invoiceNumbers = selectedInvoices.map(invoice => invoice.invoiceNumber);
    const fullInvoices = await xero.getInvoicesFromInvoiceNumbers(invoiceNumbers);

    // Create a map of invoice number to invoice ID for easy lookup
    const invoiceMap = new Map(fullInvoices.map(invoice => [
      invoice.invoiceNumber!,
      {
        invoiceId: invoice.invoiceID!,
        fullInvoice: invoice,
        createdCreditNoteIds: [] as Array<{creditNoteId: string, amount: number}>
      }
    ]));

    // Process each invoice
    for (const invoice of selectedInvoices) {
      const invoiceData = invoiceMap.get(invoice.invoiceNumber);
      if (!invoiceData) {
        throw new Error(`Invoice not found: ${invoice.invoiceNumber}`);
      }

      // First, handle deleted line items
      const deletedLines = deletedLineItems[invoice.invoiceNumber] || [];
      if (deletedLines.length > 0 && invoiceData.fullInvoice) {
        const remainingLines = invoiceData.fullInvoice.lineItems?.filter(line =>
          !deletedLines.some(deletedLine => deletedLine.lineItemID === line.lineItemID)
        ) || [];

        await xero.updateInvoiceLineItems(invoiceData.invoiceId, remainingLines);
      }

      // Ensure invoice is AUTHORISED before applying credit notes or batch payment
      if (invoiceData.fullInvoice.status === Invoice.StatusEnum.DRAFT) {
        await xero.updateBillStatus(invoiceData.invoiceId, Invoice.StatusEnum.AUTHORISED);
        // Refresh invoice data after status update
        // const updatedInvoice = await xero.getInvoice(invoiceData.invoiceId, false);
        // if (!updatedInvoice) {
        //   throw new Error(`Failed to refresh invoice after status update: ${invoice.invoiceNumber}`);
        // }
        // invoiceData.fullInvoice = updatedInvoice;
        invoiceData.fullInvoice.status = Invoice.StatusEnum.AUTHORISED;
      }

      // Then, handle credit notes
      // Get credit notes for this invoice (identified by lineItemID starting with 'credit-')
      const invoiceCreditNotes = (creditNotes[invoice.invoiceNumber] || []);
      // Store created credit notes for later inclusion in the batch payment
      const createdCreditNoteIds: Array<{creditNoteId: string, amount: number}> = [];

      if (invoiceCreditNotes.length > 0) {
        // Now create credit notes but don't allocate them to invoices
        for (const creditNote of invoiceCreditNotes) {
          if (!invoiceData.fullInvoice.contact?.contactID) {
            throw new Error(`Invoice ${invoice.invoiceNumber} is missing contact information`);
          }

          // Create a unique credit note number
          const uniqueCreditNoteNumber = `CN-${invoice.invoiceNumber}-${Date.now().toString().slice(-6)}`;

          // Create the credit note
          const creditNoteResponse = await xero.createCreditNote({
            type: CreditNote.TypeEnum.ACCRECCREDIT,
            contact: { contactID: invoiceData.fullInvoice.contact.contactID },
            date: new Date().toISOString().split('T')[0],
            creditNoteNumber: uniqueCreditNoteNumber,
            lineItems: [{
              description: `Credit Note: ${creditNote.description || creditNote.invoiceNumber}`,
              quantity: 1,
              unitAmount: creditNote.lineAmount!,
              accountCode: creditNote.accountCode || CREDIT_NODE_ACCOUNT_CODE // Use the selected account code if available
            }]
          });

          // If credit note was created successfully, store it for batch payment
          if (creditNoteResponse?.creditNotes?.[0]?.creditNoteID) {
            const creditNoteId = creditNoteResponse.creditNotes[0].creditNoteID;
            const creditNoteAmount = creditNote.lineAmount!;

            // Store in local array
            createdCreditNoteIds.push({
              creditNoteId: creditNoteId,
              amount: creditNoteAmount
            });

            // Also store in the invoiceMap for later use in batch payment
            invoiceData.createdCreditNoteIds.push({
              creditNoteId: creditNoteId,
              amount: creditNoteAmount
            });
          }
        }
      }
    }

    // The invoice amount may have changed froom the credit notes or deleted lines
    // Get updated invoice data for all invoices in a single query
    const updatedInvoices = await xero.getInvoicesFromInvoiceNumbers(invoiceNumbers);
    const updatedInvoiceMap = new Map(updatedInvoices.map(invoice => [
      invoice.invoiceNumber!,
      invoice
    ]));

    // Calculate remaining amounts for each invoice after deletions and credit notes
    const invoiceAmounts = selectedInvoices.map(invoice => {
      const updatedInvoice = updatedInvoiceMap.get(invoice.invoiceNumber);
      if (!updatedInvoice) {
        throw new Error(`Updated invoice not found: ${invoice.invoiceNumber}`);
      }

      const invoiceData = invoiceMap.get(invoice.invoiceNumber);
      if (!invoiceData) {
        throw new Error(`Invoice data not found: ${invoice.invoiceNumber}`);
      }

      return {
        invoiceId: invoiceData.invoiceId,
        amount: updatedInvoice.amountDue || 0
      };
    });

    // Create batch payment for remaining amounts
    console.log("Raw bank account ID from form:", bankAccountId);
    console.log("Default bank account ID (BluVine):", DEFAULT_BANK_ACCOUNT_ID);

    // Log all available bank accounts for debugging
    // const allBankAccounts = await getBankAccounts();
    // console.log("Available bank accounts:", JSON.stringify(allBankAccounts.accounts, null, 2));

    // // Ensure we have a valid account ID by checking different formats
    // let finalAccountID: string;

    // // First check if it's already a UUID format (like what's in cookiecadAccounts)
    // if (bankAccountId && /^[A-F0-9]{8}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{12}$/i.test(bankAccountId)) {
    //   console.log("Bank account ID is already in UUID format");
    //   finalAccountID = bankAccountId;
    // }
    // // Check if it's a known account code that maps to an ID
    // else if (bankAccountId && ACCOUNT_ID_MAP[bankAccountId]) {
    //   console.log(`Found mapping for account code ${bankAccountId} -> ${ACCOUNT_ID_MAP[bankAccountId]}`);
    //   finalAccountID = ACCOUNT_ID_MAP[bankAccountId];
    // }
    // // As a last resort, try to look it up in the accounts list
    // else if (bankAccountId && allBankAccounts.accounts) {
    //   const matchingAccount = allBankAccounts.accounts.find(acc =>
    //     acc.id === bankAccountId
    //   );

    //   if (matchingAccount) {
    //     console.log(`Found matching account: ${JSON.stringify(matchingAccount)}`);
    //     finalAccountID = matchingAccount.id;
    //   } else {
    //     console.log("No matching account found, using default");
    //     finalAccountID = DEFAULT_BANK_ACCOUNT_ID;
    //   }
    // }
    // // If all else fails, use the default
    // else {
    //   console.log("Using default bank account ID");
    //   finalAccountID = DEFAULT_BANK_ACCOUNT_ID;
    // }

    // console.log("Final account ID being used:", finalAccountID);

    // Collect all credit note IDs from each invoice
    const allCreditNoteIds: Array<{creditNoteId: string, amount: number}> = [];

    // Gather all credit notes created for each invoice
    for (const invoice of selectedInvoices) {
      const invoiceData = invoiceMap.get(invoice.invoiceNumber);
      if (invoiceData && invoiceData.createdCreditNoteIds) {
        allCreditNoteIds.push(...invoiceData.createdCreditNoteIds);
      }
    }

    // Include invoices, credit notes, and newly created invoices in the batch payment
    const allPayments = [
      ...invoiceAmounts,
      ...createdInvoiceIds
    ];

    // Credit notes will be added directly to the batch payment

    // Create reference string including all invoice numbers
    const allInvoiceNumbers = [
      ...selectedInvoices.map(i => i.invoiceNumber),
      ...Object.keys(positivePayments),
      ...Object.keys(negativePayments)
    ];

    // Create the batch payment with both invoices and credit notes
    const batchPayment = {
      account: { accountID: bankAccountId },
      date: new Date().toISOString(),
      reference: `Invoices: ${allInvoiceNumbers.join(', ')}`,
      payments: [
        // Include invoice payments
        ...allPayments.map(({ invoiceId, amount }) => ({
          invoice: { invoiceID: invoiceId },
          amount,
          reference: `Invoice ${invoiceId}`
        })),
        // Include credit note payments
        ...allCreditNoteIds.map(({ creditNoteId, amount }) => ({
          creditNote: { creditNoteID: creditNoteId },
          amount,
          reference: `Credit Note ${creditNoteId}`
        }))
      ]
    } satisfies BatchPayment;

    console.log("Batch payment object:", JSON.stringify(batchPayment, null, 2));

    await xero.createBatchPayment(batchPayment);

    // Get final invoice statuses in a single query
    // const finalInvoices = await xero.getInvoicesFromInvoiceNumbers(invoiceNumbers);
    // const finalInvoiceMap = new Map(finalInvoices.map(invoice => [
    //   invoice.invoiceNumber!,
    //   invoice
    // ]));

    const poRepo = new PurchaseOrderRepository(firebase);
    const poIds = selectedInvoices.map(invoice => invoice.poId);
    for (const poId of poIds) {
      // Update firestore
      await updateInvoicesFromXero(poId);

      // Get the updated po invoices
      const updatedPo = await poRepo.get(poId);

      if (updatedPo?.invoices) {
        // Update shopify
        const invoices = Object.values(updatedPo?.invoices || {});
        await markShopifyOrderPaid(Object.values(invoices));

        updateInvoiceShopStatus(updatedPo);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error creating deposit:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}

export async function getExpenseAccounts(): Promise<{
  success: boolean;
  error?: {
    message: string;
    type?: string;
    details?: unknown;
  };
  accounts?: Array<{code: string, name: string}>;
}> {
  try {
    const xero = new Xero();
    await xero.init();

    // Retrieve all accounts from Xero
    const allAccounts = await xero.getAccounts();

    // Filter to only active expense accounts
    const expenseAccounts = allAccounts.filter(account =>
      account.type === AccountType.EXPENSE &&
      account.status === AccountStatus.ACTIVE
    );

    return {
      success: true,
      accounts: expenseAccounts.map(account => ({
        code: account.code,
        name: account.name
      }))
    };
  } catch (error) {
    console.error('Error fetching expense accounts:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}

export async function syncXeroStatusToShopify(showArchivedAndPaid: boolean = false): Promise<DepositResult> {
  try {
    const poRepo = new PurchaseOrderRepository(firebase);
    // Only get non-archived POs unless showArchivedAndPaid is true (matching the grid behavior)
    const allPos = showArchivedAndPaid
      ? await poRepo.getAll()
      : await poRepo.getNonArchived();

    let syncedCount = 0;

    for (const po of allPos) {
      if (!po.invoices) continue;

      const invoices = Object.values(po.invoices);
      const invoicesToSync = invoices.filter(invoice =>
        invoice.status === 'PAID' && invoice.shopStatus !== 'PAID'
      );

      if (invoicesToSync.length > 0) {
        console.log(`Syncing ${invoicesToSync.length} invoices for PO ${po['po-number']}`);
        await markShopifyOrderPaid(invoicesToSync);
        await updateInvoiceShopStatus(po);
        syncedCount += invoicesToSync.length;
      }
    }

    console.log(`Successfully synced ${syncedCount} invoices from Xero to Shopify`);
    return { success: true };
  } catch (error) {
    console.error('Error syncing Xero status to Shopify:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}

// Migrated functions from reports/purchase-orders
export async function updateAllInvoicesShopStatus(showArchivedAndPaid: boolean = false): Promise<DepositResult> {
  try {
    const poRepo = new PurchaseOrderRepository(firebase);
    // Only get non-archived POs unless showArchivedAndPaid is true (matching the grid behavior)
    const pos = showArchivedAndPaid
      ? await poRepo.getAll()
      : await poRepo.getNonArchived();

    for (const po of pos) {
      if (!po.invoices) { continue; }
      await updateInvoiceShopStatus(po);
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating invoice shop status:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}

export async function markAllShopifyOrdersPaid(showArchivedAndPaid: boolean = false): Promise<DepositResult> {
  try {
    const poRepo = new PurchaseOrderRepository(firebase);
    // Only get non-archived POs unless showArchivedAndPaid is true (matching the grid behavior)
    const pos = showArchivedAndPaid
      ? await poRepo.getAll()
      : await poRepo.getNonArchived();

    for (const po of pos) {
      if (!po.invoices) { continue; }

      // Filter invoices to match grid behavior (exclude paid in both Xero and Shopify unless showArchivedAndPaid is true)
      const invoices = Object.values(po.invoices);
      const invoicesToProcess = showArchivedAndPaid
        ? invoices
        : invoices.filter(invoice =>
            !(invoice.status === 'PAID' && invoice.shopStatus === 'PAID')
          );

      if (invoicesToProcess.length > 0) {
        await markShopifyOrderPaid(invoicesToProcess);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error marking Shopify orders as paid:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}

export async function syncXeroAndFirestore(showArchivedAndPaid: boolean = false): Promise<DepositResult> {
  try {
    const poRepo = new PurchaseOrderRepository(firebase);
    // Only get non-archived POs unless showArchivedAndPaid is true (matching the grid behavior)
    const pos = showArchivedAndPaid
      ? await poRepo.getAll()
      : await poRepo.getNonArchived();

    for (const po of pos) {
      console.log("Syncing", po['po-number']);
      await updateInvoicesFromXero(po['po-number']);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    return { success: true };
  } catch (error) {
    console.error('Error syncing Xero and Firestore:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}

export async function updateShopifyPoNumbers(): Promise<DepositResult> {
  try {
    const result = await shopifyGraphQl.getManualGatewayOrders();

    for (const order of result) {
      console.log(order.name, order?.customer?.displayName);
      let customerName = order?.customer?.displayName;
      if (customerName === 'Microcenter') {
        if (order.note) {
          await shopifyGraphQl.updateOrderPoNumber(order.id, order.note);
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating Shopify PO numbers:', error);
    return {
      success: false,
      ...serializeXeroError(error)
    };
  }
}
