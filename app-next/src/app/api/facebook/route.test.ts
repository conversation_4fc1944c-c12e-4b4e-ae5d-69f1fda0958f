//@ts-nocheck

import { NextRequest } from 'next/server';
import { GET } from './route';
import { getFacebookPageAccessToken } from './utils';
import { mockFetch } from 'jest-fetch-mock';

jest.mock('./utils', () => ({
  getFacebookPageAccessToken: jest.fn(),
}));

describe('GET /api/facebook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.resetMocks();
  });

  it('should return conversations from a Facebook page', async () => {
    const mockAccessToken = 'mockAccessToken';
    const mockConversations = [
      {
        id: '123',
        participants: [{ id: '1', name: 'User 1' }],
        messages: [
          {
            id: 'm1',
            from: { id: '1', name: 'User 1' },
            to: { id: '2', name: 'Page' },
            message: 'Hello',
            created_time: '2023-01-01T00:00:00Z',
          },
        ],
      },
    ];

    (getFacebookPageAccessToken as jest.Mock).mockResolvedValue(mockAccessToken);
    mockFetch.mockResponseOnce(JSON.stringify({ data: mockConversations }));

    const request = new NextRequest('http://localhost/api/facebook?pageId=mockPageId&sinceDate=2023-01-01');
    const response = await GET(request);

    expect(response.status).toBe(200);
    const jsonResponse = await response.json();
    expect(jsonResponse).toEqual(mockConversations);
  });

  it('should return 400 if required query parameters are missing', async () => {
    const request = new NextRequest('http://localhost/api/facebook');
    const response = await GET(request);

    expect(response.status).toBe(400);
    const textResponse = await response.text();
    expect(textResponse).toBe('Missing required query parameters: pageId, sinceDate');
  });

  it('should handle API request errors', async () => {
    const mockAccessToken = 'mockAccessToken';

    (getFacebookPageAccessToken as jest.Mock).mockResolvedValue(mockAccessToken);
    mockFetch.mockResponseOnce(JSON.stringify({ error: { message: 'Error fetching conversations' } }), { status: 500 });

    const request = new NextRequest('http://localhost/api/facebook?pageId=mockPageId&sinceDate=2023-01-01');
    const response = await GET(request);

    expect(response.status).toBe(500);
    const jsonResponse = await response.json();
    expect(jsonResponse).toEqual({ error: 'Error fetching conversations' });
  });
});
