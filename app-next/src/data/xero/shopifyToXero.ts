import * as shopifyGraphQl from '~/data/shopify/shopifyGrapql';
import * as xeroUtils from './xero'; // Assuming Xero class and constants are exported
import { InvoiceSummary, PurchaseOrder } from '~/data/purchase-orders/types';
import { addPaymentTransaction } from '~/data/shopify/shopify';
import { fromShopifyGid } from '~/data/shopify/shopifyUtils';
import { PurchaseOrderRepository } from '~/data/purchase-orders/repository';
import firebase from '../google/firebase-server';

const xero = new xeroUtils.Xero();

// Keep existing functions if they are still needed elsewhere
export async function createInvoicesFromManualOrders() {
  await xero.init();
  const orders = await shopifyGraphQl.getManualGatewayOrders();
  for (const order of orders) {
    console.log(`${order.name} ${order.currentTotalPriceSet.shopMoney.amount} ${order.processedAt}`);
    try {
      if (order.currentTotalPriceSet.shopMoney.amount === 0) { continue; }
      await new Promise(resolve => setTimeout(resolve, 500)); //Slow down requests
      // This old logic might need updating or removal based on new flow
      await xero.createInvoice({
        invoiceNumber: order.name.replace('#', ''),
        date: new Date(order.processedAt),
        dueDate: new Date(order.processedAt),
        updatedDateUTC: new Date(order.processedAt),
        lineItems: [{
          description: 'Manual order items',
          lineAmount: order.currentTotalPriceSet.shopMoney.amount,
          accountCode: 'shopManual' // Might need changing to shopRevenue
        }],
        contact: {
          name: order.customer?.displayName || 'General',
          contactNumber: order.customer?.id,
          emailAddress: order.customer.email
        }
      });
    }
    catch (error: any) {
      if (error.Elements) {
        console.error(`${error.Type} - ${error.Elements?.map((element: any) => element.ValidationErrors.map((validationError: any) => validationError.Message)).join(', ')}`);
      }
      else {
        console.error(error);
      }
    }
  }
}

export async function markShopifyOrderPaid(invoices: InvoiceSummary[]) {
  for (const invoice of invoices) {
    if (invoice.status == 'PAID' && invoice.shopStatus != 'PAID') {
      let orderId = invoice?.shopId;
      if (orderId) {
        let order = await shopifyGraphQl.getOrderById(orderId);
        if (order) {
          if (order.displayFinancialStatus != 'PAID') {
            let outstandingAmount = order.totalOutstandingSet.shopMoney.amount;
            if (invoice.paid && invoice.paidDate) {
              console.log(`Marking order ${orderId} as paid`);
              let orderIdNumber = Number(fromShopifyGid(orderId));
              await addPaymentTransaction(orderIdNumber, invoice.paidDate);
            }
          }
        }
      }
    }
  }
}

export async function updateInvoiceShopStatus(po: PurchaseOrder) {
  const purchaseOrderRepository = new PurchaseOrderRepository(firebase);

  for (const [invoiceId, invoice] of Object.entries(po.invoices) as [string, InvoiceSummary][]) {
    if (invoice.shopStatus == 'PAID') { continue; }

    let orderId = invoice?.shopId;
    if (orderId) {
      let order = await shopifyGraphQl.getOrderById(orderId);
      if (order) {
        if (order.displayFinancialStatus == 'PAID' && order.totalOutstandingSet.shopMoney.amount == 0) {
          console.log(`Updating ${po['po-number']} invoice ${invoice.invoiceNumber} to PAID`);
          invoice.shopStatus = 'PAID';
          await purchaseOrderRepository.update(po['po-number'], {
            [`invoices.${invoiceId}.shopStatus`]: 'PAID'
          });
        }
      }
    }
  }
}


/**
 * Fetches Shopify manual gateway orders updated/created after a specific date,
 * creates direct revenue invoices in Xero (posting to shopRevenue),
 * and optionally attempts to fix previously created $0 invoices.
 * Sets invoices to AUTHORISED status.
 * @param startDate - The date string (YYYY-MM-DD) to fetch orders from.
 * @param updateExisting - Optional flag (default false) to enable updating existing $0 invoices.
 */
export async function createInitialXeroInvoicesFromShopify(startDate: string, updateExisting: boolean = false) {
  console.log(`Starting Shopify to Xero DIRECT REVENUE invoice sync for orders from ${startDate}...`);
  await xero.init();

  // Step 1: Fetch Orders from Shopify (manual gateway, filtered by date)
  const ordersResult = await shopifyGraphQl.getOrdersByDates(startDate); // Ensure this uses the query with gateway:manual
  const orders = ordersResult.filter((o) =>
    (((o.paymentGatewayNames.includes('manual') && o.paymentGatewayNames.length == 1)
      || o.paymentGatewayNames.length == 0))
    && o.currentTotalPriceSet.shopMoney.amount > 0
    && !(o.transactions.length==0)
  )
  console.log(`Fetched ${orders.length} manual gateway orders from Shopify.`);

  let createdCount = 0;
  let updatedCount = 0; // For fixed $0 invoices
  let skippedExistingCount = 0; // For existing non-$0 invoices or when updateExisting is false
  let skippedUnpaidCount = 0;
  let skippedPartialPaymentCount = 0;
  let errorCount = 0;

  for (const order of orders) {
    try {
      const orderTotal = order.currentTotalPriceSet.shopMoney.amount;
      const orderName = order.name.replace('#', ''); // Use order number without #
      const invoiceNumber = orderName; // Use only the order number

      console.log(`Processing Order: ${order.name} (${order.id}), Total: ${orderTotal}, Target Invoice #: ${invoiceNumber}`);

      // --- Basic Order Validation ---
      if (orderTotal <= 0) {
        console.log(`Skipping order ${order.name}: Zero total.`);
        skippedUnpaidCount++;
        continue;
      }

      const paymentTransactions = order.transactions.filter(
        (t) => (t.kind === 'SALE' || t.kind === 'CAPTURE') && t.status === 'SUCCESS'
      );
      const totalPaidInShopify = paymentTransactions.reduce(
        (sum, t) => sum + t.amountSet.shopMoney.amount, 0
      );

      if (totalPaidInShopify === 0) {
        console.log(`Skipping order ${order.name}: No successful payment transactions found.`);
        skippedUnpaidCount++;
        continue;
      }

      const epsilon = 0.001; // Tolerance for float comparison
      if ((orderTotal - totalPaidInShopify) > epsilon) {
        console.warn(`Skipping order ${order.name}: Partial payment detected (Paid: ${totalPaidInShopify}, Total: ${orderTotal}). Requires manual review.`);
        skippedPartialPaymentCount++;
        continue;
      }
      console.log(`Order ${order.name} confirmed as fully paid in Shopify.`);

      // --- Define Desired Invoice State ---
      const customerName = order.customer?.displayName || 'Unknown Customer';
      const desiredReference = `${order.name} - ${customerName}`;
      const desiredLineItems = [{
        description: `Shopify Order ${order.name} Items`,
        lineAmount: orderTotal,
        accountCode: xeroUtils.cookiecadAccounts.shopRevenue, // Direct to final revenue
      }];

      // --- Check for Existing Invoice ---
      await new Promise(resolve => setTimeout(resolve, 500)); // Rate limiting
      const existingInvoices = await xero.getInvoices({ invoiceNumbers: [invoiceNumber] });
      const activeExistingInvoices = existingInvoices.filter(inv => inv.status !== xeroUtils.invoiceStatusEnum.DELETED && inv.status !== xeroUtils.invoiceStatusEnum.VOIDED);

      // --- Handle Existing Invoice (if updateExisting is true) ---
      if (updateExisting && activeExistingInvoices.length > 0) {
        const existingInvoice = activeExistingInvoices[0];

        // Check if it's a $0 invoice that needs fixing
        if (existingInvoice.total === 0) {
          console.log(`Found existing $0 invoice ${invoiceNumber} (Status: ${existingInvoice.status}). Attempting to update details and authorise...`);
          try {
            if (!existingInvoice.invoiceID) {
              throw new Error(`Existing $0 invoice ${invoiceNumber} found but lacks an ID.`);
            }
            // Use the method to update status, reference, and line items
            await xero.updateInvoiceDetails(
              existingInvoice.invoiceID,
              xeroUtils.invoiceStatusEnum.AUTHORISED,
              desiredReference,
              desiredLineItems
            );
            console.log(`Successfully updated details and authorised existing $0 invoice ${invoiceNumber}.`);
            updatedCount++;
          } catch (updateError: any) {
             // Improved error logging for Xero API errors
             if (updateError.response?.body?.Elements) {
               const errorMessages = updateError.response.body.Elements.map((element: any) =>
                 element.ValidationErrors?.map((validationError: any) => validationError.Message).join(', ') || 'Unknown validation error'
               ).join('; ');
               console.error(`Error updating/authorising existing $0 invoice ${invoiceNumber}: Xero API Error - ${errorMessages}`);
             } else if (updateError.response?.body) {
               console.error(`Error updating/authorising existing $0 invoice ${invoiceNumber}: API Response Error -`, JSON.stringify(updateError.response.body, null, 2));
             } else {
               console.error(`Error updating/authorising existing $0 invoice ${invoiceNumber}:`, updateError.message || updateError);
             }
             errorCount++;
          }
        } else {
          // It's an existing, non-zero invoice. Skip it.
          console.log(`Skipping order ${order.name}: Non-zero invoice ${invoiceNumber} already exists (Total: ${existingInvoice.total}, Status: ${existingInvoice.status}).`);
          skippedExistingCount++;
        }
        continue; // Move to the next order after handling the existing one
      } else if (activeExistingInvoices.length > 0) {
        // updateExisting is false, so just skip
        console.log(`Skipping order ${order.name}: Invoice ${invoiceNumber} already exists and updateExisting is false.`);
        skippedExistingCount++;
        continue; // Move to the next order
      }

      // --- Create New Invoice ---
      console.log(`Creating direct revenue invoice ${invoiceNumber} for order ${order.name}...`);
      const invoiceInput = {
        invoiceNumber: invoiceNumber,
        date: new Date(order.processedAt),
        dueDate: new Date(order.processedAt), // Keep due date same as processed date
        updatedDateUTC: new Date(),
        reference: desiredReference,
        contact: {
          contactID: xeroUtils.cookiecadContacts.shopify.contactID
        },
        lineItems: desiredLineItems
      };

      try {
        const createResponse = await xero.createInvoice(invoiceInput);

        if (createResponse?.body?.invoices && createResponse.body.invoices.length > 0 && createResponse.body.invoices[0].invoiceID) {
          const newInvoiceId = createResponse.body.invoices[0].invoiceID;
          console.log(`Successfully created DRAFT invoice ${invoiceNumber} (ID: ${newInvoiceId}). Attempting to set status to AUTHORISED...`);

          try {
            // Immediately update status to AUTHORISED
            await xero.updateBillStatus(newInvoiceId, xeroUtils.invoiceStatusEnum.AUTHORISED);
            console.log(`Successfully set invoice ${invoiceNumber} status to AUTHORISED.`);
            createdCount++;
          } catch (authError: any) {
             console.error(`Error setting status to AUTHORISED for new invoice ${invoiceNumber}:`, authError.message || authError);
             errorCount++; // Count error even if draft was created
          }
        } else {
          console.error(`Invoice creation for ${invoiceNumber} failed or did not return an ID. Response:`, JSON.stringify(createResponse?.body, null, 2));
          errorCount++;
        }
      } catch (createError: any) {
         // Improved error logging for Xero API errors
         if (createError.response?.body?.Elements) {
           const errorMessages = createError.response.body.Elements.map((element: any) =>
             element.ValidationErrors?.map((validationError: any) => validationError.Message).join(', ') || 'Unknown validation error'
           ).join('; ');
           console.error(`Error creating invoice ${invoiceNumber}: Xero API Error - ${errorMessages}`);
         } else if (createError.response?.body) {
           console.error(`Error creating invoice ${invoiceNumber}: API Response Error -`, JSON.stringify(createError.response.body, null, 2));
         } else {
           console.error(`Error creating invoice ${invoiceNumber}:`, createError.message || createError);
         }
         errorCount++;
      }

    } catch (error: any) {
      // Catch errors from initial processing before API calls
      errorCount++;
      console.error(`Error processing order ${order.name}:`, error.message || error);
    }
    // Add a small delay between processing each order
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log("\n--- Sync Summary ---");
  console.log(`Processed Orders: ${orders.length}`);
  console.log(`Invoices Created (New): ${createdCount}`);
  console.log(`Invoices Updated (Fixed $0): ${updatedCount}`);
  console.log(`Skipped (Existing Non-$0 or Updates Disabled): ${skippedExistingCount}`);
  console.log(`Skipped (Unpaid/Zero Total): ${skippedUnpaidCount}`);
  console.log(`Skipped (Partial Payment): ${skippedPartialPaymentCount}`);
  console.log(`Errors Encountered: ${errorCount}`);
  console.log("------------------\n");
}


/**
 * Finds Xero ACCREC invoices based on specified criteria (prefix or contains),
 * start date, and specific statuses (DRAFT, SUBMITTED, AUTHORISED, PAID).
 * Returns a list of these invoices for review before potential deletion/voiding.
 * @param searchType - How to search ('prefix' or 'contains').
 * @param value - The value to search for (prefix string or contains text).
 * @param startDate - The start date for the search (YYYY-MM-DD string).
 */
export async function findOldInvoicesByCriteria(searchType: 'prefix' | 'contains', value: string, startDate: string) {
  // Validate startDate format (basic check)
  if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
    throw new Error('Invalid startDate format. Please use YYYY-MM-DD.');
  }

  const criteriaDescription = searchType === 'prefix'
    ? `invoice number starting with "${value}"`
    : `reference containing "${value}"`;
  console.log(`Finding invoices (DRAFT, SUBMITTED, AUTHORISED, PAID) from ${startDate} with ${criteriaDescription}...`);
  await xero.init();

  // Use the type defined earlier for consistency
  const foundInvoices: Array<{ invoiceNumber: string | undefined, invoiceID: string | undefined, status: string | undefined, total: number | undefined }> = [];
  let fetchErrorCount = 0; // Renamed for clarity
  let fetchErrorMessage: string | null = null;

  // Define target statuses outside the try block for broader scope
  // Use the correct type for the enum array
  // Use the exported type alias from xero.ts
  const targetStatuses: xeroUtils.InvoiceStatus[] = [
      xeroUtils.invoiceStatusEnum.DRAFT,
      xeroUtils.invoiceStatusEnum.SUBMITTED,
      xeroUtils.invoiceStatusEnum.AUTHORISED,
      xeroUtils.invoiceStatusEnum.PAID
  ];
  const targetStatusesString = targetStatuses.join(','); // For logging

  try {
    // Construct the where clause dynamically including the date
    const [year, month, day] = startDate.split('-').map(Number);
    let whereClause = `Type=="ACCREC" && Date >= DateTime(${year}, ${month}, ${day})`;

    if (searchType === 'prefix') {
      const escapedValue = value.replace(/"/g, '\\"'); // Escape quotes
      whereClause += ` && InvoiceNumber.StartsWith("${escapedValue}")`;
    } else { // searchType === 'contains'
      const escapedValue = value.replace(/"/g, '\\"'); // Escape quotes
      whereClause += ` && Reference.Contains("${escapedValue}")`;
    }
    console.log(`Fetching invoices with statuses [${targetStatusesString}] and where: ${whereClause}`);

    // Use the 'statuses' parameter with the array of enums
    const invoices = await xero.getInvoices({
        where: whereClause,
        statuses: targetStatuses // Pass the array directly
    });
    console.log(`API returned ${invoices.length} invoices matching criteria.`);

    // No need for local status filtering anymore, API handles it via 'statuses' param
    for (const invoice of invoices) {
        if (!invoice.invoiceID) {
          console.warn(`Invoice ${invoice.invoiceNumber} returned but lacks an ID. Skipping.`);
          // Optionally count this as a specific type of issue if needed
          continue;
        }
        // Log and add directly
        console.log(`Found candidate: ${invoice.invoiceNumber} (ID: ${invoice.invoiceID}, Status: ${invoice.status}, Total: ${invoice.total})`);
        foundInvoices.push({
          invoiceNumber: invoice.invoiceNumber,
          invoiceID: invoice.invoiceID,
          status: invoice.status?.toString(), // Convert enum to string
          total: invoice.total
        });
    }

  } catch (fetchError: any) {
    fetchErrorMessage = fetchError.message || 'Unknown fetch error';
    console.error('Error fetching invoices for review:', fetchErrorMessage);
    fetchErrorCount++; // Count fetch error
  }

  console.log("\n--- Find Summary ---");
  console.log(`Found ${foundInvoices.length} invoices (Statuses: ${targetStatusesString}) from ${startDate} matching criteria: ${criteriaDescription}.`);
  console.log(`Errors Encountered during fetch: ${fetchErrorCount}`);
  console.log("------------------\n");

  // Return detailed results including potential fetch error
  return {
    count: foundInvoices.length,
    invoices: foundInvoices,
    errorCount: fetchErrorCount, // Use renamed variable
    fetchError: fetchErrorMessage // Include fetch error message
  };
}


/**
 * Processes a list of found invoices: Deletes DRAFT, Voids AUTHORISED and PAID.
 * @param invoicesToProcess - Array of invoice objects { invoiceID, status, invoiceNumber }
 * @returns Object with counts of deleted, voided, and errors, plus specific error details.
 */
export async function processFoundShopInvoices(
  invoicesToProcess: Array<{ invoiceID: string | undefined, status: string | undefined, invoiceNumber: string | undefined }>
) {
  console.log(`Processing ${invoicesToProcess.length} found invoices (Delete DRAFT, Void AUTHORISED/PAID)...`);
  await xero.init();

  let deletedCount = 0;
  let voidedCount = 0;
  let errorCount = 0;
  const errors: Array<{ invoiceNumber: string | undefined, error: string }> = [];

  for (const invoice of invoicesToProcess) {
    if (!invoice.invoiceID) {
      console.warn(`Skipping invoice with missing ID (Number: ${invoice.invoiceNumber}).`);
      errorCount++;
      errors.push({ invoiceNumber: invoice.invoiceNumber, error: 'Missing Invoice ID' });
      continue;
    }

    try {
      await new Promise(resolve => setTimeout(resolve, 600)); // Rate limiting

      // Process based on status
      const statusStr = invoice.status?.toString(); // Ensure status is a string for comparison

      if (statusStr === xeroUtils.invoiceStatusEnum.DRAFT.toString()) {
        console.log(`Attempting to delete DRAFT invoice ${invoice.invoiceNumber} (ID: ${invoice.invoiceID})...`);
        await xero.deleteInvoice(invoice.invoiceID);
        console.log(`Successfully deleted DRAFT invoice ${invoice.invoiceNumber}.`);
        deletedCount++;
      } else if (statusStr === xeroUtils.invoiceStatusEnum.AUTHORISED.toString() || statusStr === xeroUtils.invoiceStatusEnum.PAID.toString()) {
        // Void both AUTHORISED and PAID invoices
        console.log(`Attempting to void ${statusStr} invoice ${invoice.invoiceNumber} (ID: ${invoice.invoiceID})...`);
        await xero.voidInvoice(invoice.invoiceID); // Use voidInvoice for both
        console.log(`Successfully voided ${statusStr} invoice ${invoice.invoiceNumber}.`);
        voidedCount++;
      } else {
        // Handle other statuses found (e.g., SUBMITTED) or unexpected ones
        console.warn(`Skipping invoice ${invoice.invoiceNumber} with status: ${statusStr}. No action defined.`);
        // Optionally add to errors if this shouldn't happen based on the find query
        // errors.push({ invoiceNumber: invoice.invoiceNumber, error: `Skipped status: ${statusStr}` });
        // errorCount++;
      }
    } catch (processError: any) {
      errorCount++;
      let errorMessage = processError.message || 'Unknown error';
      // Extract detailed Xero validation errors if available
      if (processError.response?.body?.Elements) {
        const validationMessages = processError.response.body.Elements.map((element: any) =>
          element.ValidationErrors?.map((validationError: any) => validationError.Message).join(', ') || 'Unknown validation error'
        ).join('; ');
        errorMessage = `Xero API Error: ${validationMessages}`;
        console.error(`Failed to process invoice ${invoice.invoiceNumber}: ${errorMessage}`);
      } else if (processError.response?.body) {
        errorMessage = `API Response Error: ${JSON.stringify(processError.response.body)}`;
        console.error(`Failed to process invoice ${invoice.invoiceNumber}: ${errorMessage}`);
      } else {
        console.error(`Failed to process invoice ${invoice.invoiceNumber}:`, errorMessage);
      }
      errors.push({ invoiceNumber: invoice.invoiceNumber, error: errorMessage });
    }
  }

  console.log("\n--- Processing Summary ---");
  console.log(`Invoices Deleted (DRAFT): ${deletedCount}`);
  console.log(`Invoices Voided (AUTHORISED/PAID): ${voidedCount}`);
  console.log(`Errors Encountered: ${errorCount}`);
  if (errors.length > 0) {
      console.log("Errors:", JSON.stringify(errors, null, 2));
  }
  console.log("------------------------\n");

  return {
    deletedCount,
    voidedCount,
    errorCount,
    errors
  };
}
/**
 * Deletes all Xero invoices with invoice numbers starting with 'shop-'.
 * Used by the /api/xero/delete-old-shop-invoices endpoint.
 */
export async function deleteOldShopPrefixedInvoices() {
  // Find all invoices prefixed with 'shop-' from the beginning of time
  const criteriaResult = await findOldInvoicesByCriteria('prefix', 'shop-', '1970-01-01');
  const invoicesToProcess = (criteriaResult.invoices || [])
    .filter(inv => inv.invoiceID)
    .map(inv => ({
      invoiceID: inv.invoiceID!,
      status: inv.status,
      invoiceNumber: inv.invoiceNumber
    }));
  const processResult = await processFoundShopInvoices(invoicesToProcess);
  return {
    attemptedDeletions: processResult.deletedCount,
    skipped: processResult.voidedCount,
    errors: processResult.errors,
  };
}
