import type { PurchaseOrderStatus } from "~/data/purchase-orders/types"; 
export type { PurchaseOrderStatus } from "~/data/purchase-orders/types"; 


export enum InvoiceStatus {
  NEW = "NEW",
  PAID = "PAID",
  PARTIALLY_PAID = "PARTIALLY_PAID",
  VOID = "VOID"
}

export interface PurchaseOrderItem {
  id: string;
  sku: string;
  quantity: number;
  amount: number;
  outstanding: number;
  invoicedQuantity: number;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
  }
}

export interface XeroLineItem {
  lineItemID?: string;
  description?: string;
  lineAmount?: number;
  accountCode?: string;
  }
