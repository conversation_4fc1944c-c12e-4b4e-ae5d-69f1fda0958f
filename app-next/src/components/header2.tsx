"use client";

import React, { useState, useEffect } from "react";
import { useFirebaseAuth } from "../auth/firebase";
import { signOut } from "firebase/auth";
import { useAuth } from "../auth/context";
import { usePathname } from 'next/navigation';
import Link from "next/link"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "~/components/ui/dropdown-menu"
import { Button } from "~/components/ui/button"
import { Sheet, SheetTrigger, SheetContent } from "~/components/ui/sheet"
import LoginUI from "~/components/cookiecadLogin"

export default function Header() {
  const { user } = useAuth();
  console.log("user", user)
  const [isLogoutLoading, setIsLogoutLoading] = useState(false);
  const [hasLoggedOut, setHasLoggedOut] = useState(false);
  const auth = useFirebaseAuth();
  const pathname = usePathname();

  const navItems = [
    { label: "Inventory", items: [
      { href: "/inventory", label: "Inventory" },
      { href: "/inventory/projections", label: "Projections" },
      { href: "/inventory/warehouse", label: "Warehouse" },
      { href: "/amazon/listings", label: "Amz Listings"},
      { href: "/products", label: "Products" },
      { href: "/purchases", label: "Purchases" },
    ] },
    { href: "/shipping", label: "Ward Shipping" },
    { label: "Reports", items: [
      { href: "/reports", label: "Amazon Report"},
      { href: "/reports/shopify-orders", label: "Shopify Orders"},
      { href: "/reports/shopify-orders-monthly", label: "Shopify Orders Monthly"},
    ] },
    { label: "POs", items: [
      { href: "/purchase-orders", label: "Purchase Orders" },
      { href: "/purchase-orders/outstanding", label: "Outstanding" },
      { href: "/purchase-orders/po-invoices", label: "Invoices" },
    ] },
    { label: "Sales", items: [
      { href: "/sales", label: "Sales" },
      { href: "/orders", label: "Orders" },
      { href: "/orders/shopify-tx", label: "Shopify TX" },
      { href: "/orders/cutters", label: "Cutter Orders" },
    ] },
    { href: "/users", label: "Users" },
    { label: "Utils", items: [
      { href: "/utils", label: "Utils" },
      { href: "/utils/accounting", label: "Accounting" },
      { href: "/utils/recovery", label: "Firestore Recovery" },
      { href: "/utils/facebook-test", label: "Facebook Test" },
      { href: "/utils/duplicate-emails", label: "Duplicate Emails" },
      { href: "/utils/stripe", label: "Stripe" },
      { href: "/utils/cutter-library", label: "Cutter Library" },
    ] },
  ];

  const handleLogout = async () => {
    setIsLogoutLoading(true);
    try {
      await signOut(auth.getFirebaseAuth());
      setHasLoggedOut(true);
      await fetch("/api/logout", {
        method: "GET",
      });
      window.location.reload();
    } catch (error) {
      console.error(error);
    } finally {
      setIsLogoutLoading(false);
    }
  };

  useEffect(() => {
    if (hasLoggedOut) {
      // Perform any additional actions if needed
    }
  }, [hasLoggedOut]);

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white dark:border-gray-800 dark:bg-gray-950">
      <div className="container mx-auto flex h-16 max-w-6xl items-center justify-between px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2" prefetch={false}>
          <span className="text-xl font-bold">Cookiecad</span>
        </Link>
        <nav className="hidden items-center gap-6 text-sm font-medium md:flex">
          {navItems.map((navItem) => (
            <div key={navItem.href || navItem.label}>
              {navItem.href ? (
                <Link
                  href={navItem.href}
                  className={`text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-50 ${
                    navItem.href === pathname ? "font-bold" : ""
                  }`}
                  prefetch={false}
                >
                  {navItem.label}
                </Link>
              ) : (
                <DropdownMenu modal={false}>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="p-0">
                      {navItem.label}
                      <ChevronDownIcon className="ml-1 h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {navItem.items && navItem.items.map((item) => (
                      <Link key={item.href} href={item.href} className="w-full">
                        <DropdownMenuItem className="cursor-pointer">
                          {item.label}
                        </DropdownMenuItem>
                      </Link>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          ))}
        </nav>
        <div className="flex items-center gap-4">
          {user && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Logged in as {user.email}
              </span>
              {/* <Button
                variant="outline"
                size="sm"
                disabled={isLogoutLoading}
                onClick={handleLogout}
              >
                Log out
              </Button> */}
            </div>
          // ) : (
          //   <LoginUI />
          //   // <Link href="/login">
          //   //   <Button
          //   //     variant="outline"
          //   //     size="sm"
          //   //   >
          //   //     Log in
          //   //   </Button>
          //   // </Link>
          )}
          <LoginUI />
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <MenuIcon className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <nav className="flex flex-col gap-4">
                {navItems.map((navItem) => (
                  <div key={navItem.href || navItem.label}>
                    {navItem.href ? (
                      <Link
                        href={navItem.href}
                        className={`text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-50 ${
                          navItem.href === pathname ? "font-bold" : ""
                        }`}
                        prefetch={false}
                      >
                        {navItem.label}
                      </Link>
                    ) : (
                      <DropdownMenu modal={false}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="p-0 justify-start">
                            {navItem.label}
                            <ChevronDownIcon className="ml-1 h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          {navItem.items && navItem.items.map((item) => (
                            <Link key={item.href} href={item.href} className="w-full">
                              <DropdownMenuItem className="cursor-pointer">
                                {item.label}
                              </DropdownMenuItem>
                            </Link>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                ))}
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}

function ChevronDownIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="m6 9 6 6 6-6" />
    </svg>
  )
}

function MenuIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="4" x2="20" y1="12" y2="12" />
      <line x1="4" x2="20" y1="6" y2="6" />
      <line x1="4" x2="20" y1="18" y2="18" />
    </svg>
  )
}
