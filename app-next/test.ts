import './dotenv';

import { amazonAds } from './src/data/amazon/amz-ads';
import firebase from './src/data/google/firebase-server';
import { addUPC } from '~/app/utils/addSkuMapsUtil';
import { addShopifyVariantIdsToFirestore } from '~/app/products/productsUtils'
import { shopifyNode } from 'cookiecad-shared'
import * as firestoreShopify from './src/data/google/firstore-shopify';
import * as shopifyGraphQl from './src/data/shopify/shopifyGrapql';
import * as shopifyRest from './src/data/shopify/shopify';
import {createInvoicesFromManualOrders} from './src/data/xero/shopifyToXero';
import {uploadPurchase} from '~/app/purchases/purchases'
import {parseFile as parseTable} from '~/data/aws/process-textract-result'
import {SPApi} from '~/data/amazon/amazon-seller-reports'
import * as users from '~/app/users/userData'
import fs from 'fs';
// import * as firestoreUtils from '~/data/google/firestore-utils'
import firebaseAdmin from "firebase-admin";
import { FieldValue, Timestamp } from 'firebase-admin/firestore';
// import * as expensesCsvToXero from '~/data/xero/expensesCsvToXero'

import {writeUrlMapToFile} from '~/js/parseSitemap'
import * as ward from '~/app/api/shipping/ward'

import * as poUtils from '~/app/utils/poUtils'

const firestore = firebaseAdmin.firestore();

async function getInvoices() {
  const result = await amazonAds.getInvoices();
  for (const invoice of result.payload.invoiceSummaries) {
    firebase.addData('amazon-ads-invoices', invoice);
  }
  console.log(result);
}


async function getDraftOrders() {
  const result = await shopifyNode.getDraftOrder(1149066871035);
  console.log(JSON.stringify(result, null, 2));
}

async function saveToDisk(data: any) {
  const filename = String.raw`/home/<USER>/Downloads/output.json`;
  fs.writeFileSync(filename, JSON.stringify(data, null, 2));
  console.log(`File written to ${filename}`);
}

async function execSpApi() {
  const spApi = new SPApi;
  await spApi.init('US');
  // spApi.createReport('GET_RESTOCK_INVENTORY_RECOMMENDATIONS_REPORT').then(result => { console.log(result); } );  
  //spApi.getReportByType('GET_RESTOCK_INVENTORY_RECOMMENDATIONS_REPORT').then(result => { console.log(JSON.stringify(result?.data?.reports[0])); } );
  // spApi.getReportDocument('amzn1.spdoc.1.4.na.6691b1a0-dbdf-471a-b55f-cc319f164d95.T1CY7X7GZSB0YU.94300').then(result => { 
 //   console.log(JSON.stringify(result?.data)); } );
 spApi.getFirstReport('GET_RESTOCK_INVENTORY_RECOMMENDATIONS_REPORT').then(result => { console.log(result); } );
}

// amazonAds.grant(); //Then go to this url to get the token
// amazonAds.authorize('ANfvolSZJPunoEwPvXau');

// poUtils.syncXeroAndFirestore();


// firebase.getAll('purchase-orders').then(async (pos: any) => {
//   for (const po of pos) {
//     console.log("Calculating totals for", po.id);
//     let poItems = await firebase.getData('purchase-orders', 'purchase-order-items', po.id);
//     let outstandingTotal = 0;
//     let total = 0;
//     let amount = 0;
//     for (const poItem of poItems) {
//       let quantity = poItem.quantity;
//       let invoices = Object.values(poItem.invoices || [])
//       let invoicedQuantity = invoices.reduce((acc: number, i: any) => acc + i.quantity, 0) || 0
//       let outstanding = quantity - invoicedQuantity;
//       let newData = { outstanding: outstanding }
//       await firebase.updateData(`purchase-orders/${po.id}/purchase-order-items`, poItem.id, newData);
//       outstandingTotal += outstanding;
//       total += quantity;
//       amount += poItem.amount * quantity;
//     }
//     await firebase.updateData('purchase-orders', po.id, { 
//       total: total,
//       amount: Math.round(amount * 100) / 100,
//       outstandingTotal: outstandingTotal 
//     });
//   }
// });
  

// poDataUtil.archivePaidCompletedPos();

// shopifyGraphQl.updateOrderPoNumber('gid://shopify/Order/5354270458107', '93272');
// poDataUtil.updateInvoiceFromShopify('93272', null);



// textract(String.raw`/home/<USER>/Downloads/PI+JCNW20240710.pdf`)
//   .then(result => { 
//     let parsed = parseFile(result);
//     saveToDisk(parsed)
//     let processed = parseTable(parsed);
//     fs.writeFileSync('/home/<USER>/Downloads/output1.json', JSON.stringify(processed, null, 2));
//    } )
//   .catch(err => { console.log(err.response || err); });

// shopifyRest.voidTransaction('5354270458107', '6613530411259').then(result => { console.log(result); });

// firestoreShopify.pushOrdersWithTransactions(firebase);

// shopifyGraphQl.getOrdersWithTransactionsFromDate('2024-03-14', 10).then(result => { console.log(result); } );

// createInvoicesFromManualOrders();

// generateShopifyOrderLineItems(firebase).then(result => { console.log(result); });

// addShopifyVariantIdsToFirestore();
// addUPC();
// getDraftOrders();

// firestoreShopify.generateShopifyOrderMonthly(firebase).then(result => {
//   console.log(result);
// });

// firestoreShopify.addLineItemsToOrders(firebase).then(result => { console.log(result); });

//getInvoices();


// execSpApi();

// let sitemap = fs.readFileSync(String.raw`C:\Users\<USER>\Downloads\sitemap_pages_1.xml`, 'utf8');
// writeUrlMapToFile(sitemap);

// let anHourAgo = new Date()
// anHourAgo.setHours(anHourAgo.getHours() - 1)
// let query = firestoreUtils.querySnapshot(firebaseAdmin.firestore().collection('purchase-orders').doc('04211').collection('purchase-order-items'), anHourAgo).then(result => {
//   console.log(result);
//   const data = result.docs.map(doc => doc.data());
//   console.log("data", data);
//   data.forEach(item => {
//     firebase.setData('purchase-orders/4211/purchase-order-items', item.sku, item)
//   })
// });

// let doc = firestoreUtils.documentSnapshot('purchase-orders/04211/purchase-order-items', anHourAgo).then(result => {
//   console.log(result);
//   const data = result.data();
//   console.log("data", data);
//   firebase.setData('purchase-orders', '4211', data);
// });


// firestoreUtils.moveDoc('purchase-orders', '04211', 'purchase-orders', {id: '4211'})


//ward.imagingRequest('023-0751610')

// expensesCsvToXero.importAmazonBill(String.raw`/home/<USER>/Downloads/orders_from_20230702_to_20240702_20240702_0937.csv`)

// Do a dummy write to every item to trigger the trigger
// let collectionGroup = firebaseAdmin.firestore().collectionGroup('purchase-order-items').get().then(result => {
//   result.forEach(doc => {
//     console.log(doc.id);
//     doc.ref.update({
//       quantity: FieldValue.increment(1) // Update with a new timestamp
//     }).then(() => {
//       // Cleanup immediately after the update
//       doc.ref.update({
//         quantity: FieldValue.increment(-1) // Remove the trigger field
//       })
//     });
//   });
// });


//Recover data
const now = new Date(Date.now() - 300 * 60 * 1000); // 20 minutes ago
now.setSeconds(0, 0); // Round to the nearest minute
const readTime = Timestamp.fromDate(now);
recoverData().catch(console.error);

async function recoverData() {
  const recoverDoc = (querySnapshot: any) =>   querySnapshot.forEach((doc: any) => {
    console.log(`Recovering document: ${doc.id}`);
    const data = doc.data();
    console.log(data);
    // firestore.doc(doc.ref.path).set(data)
  });


  const collection = firestore.collection('purchase-orders');
  const querySnapshotPo = await firestore.runTransaction(async (transaction) => {
    return await transaction.get(collection);
  }, { readOnly: true, readTime });
  recoverDoc(querySnapshotPo);

  const collectionGroup = firestore.collectionGroup('purchase-order-items');
  const querySnapshot = await firestore.runTransaction(async (transaction) => {
    return await transaction.get(collectionGroup);
  }, { readOnly: true, readTime });
  recoverDoc(querySnapshot);

}




async function getUsers() {
  let token = await users.getToken();
  try {
    let userList = await users.getUsers(token, '<EMAIL>')
    console.log(JSON.stringify(userList, null, 2));
  }
  catch(e: any) {
    if (e.response?.data) {
      console.trace(e.response?.data)
    }
    else {
      console.trace(e)
    }
  }
}
// getUsers();